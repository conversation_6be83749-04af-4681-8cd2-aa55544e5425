#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Unicode字符标准化功能
"""

from process_labels_with_dify import DifyLabelProcessor

def test_unicode_normalization():
    """测试Unicode字符标准化"""
    
    # 创建处理器实例
    processor = DifyLabelProcessor(
        dify_api_key="test",
        workflow_id="test",
        input_json_path="test.json",
        output_json_path="test_output.json"
    )
    
    # 测试用例
    test_cases = [
        {
            "name": "下标数字问题",
            "input": "3/₇ + 2/₅ = ?",
            "expected": "3/7 + 2/5 = ?"
        },
        {
            "name": "上标数字问题", 
            "input": "x² + y³ = z⁴",
            "expected": "x2 + y3 = z4"
        },
        {
            "name": "混合上下标",
            "input": "H₂O + CO₂ → C₆H₁₂O₆",
            "expected": "H2O + CO2 → C6H12O6"
        },
        {
            "name": "数学符号",
            "input": "5\u00d73\u00f72\u22121\uff0b4\uff1d?",
            "expected": "5*3/2-1+4=?"
        },
        {
            "name": "全角括号",
            "input": "\uff08\u0033\uff0b\u0034\uff09\u00d7\uff08\u0035\uff0d\u0032\uff09\uff1d\uff1f",
            "expected": "(3+4)*(5-2)=?"
        },
        {
            "name": "全角标点",
            "input": "小明有5个苹果\uff0c吃了2个\uff0c还剩几个\uff1f",
            "expected": "小明有5个苹果,吃了2个,还剩几个?"
        },
        {
            "name": "引号问题",
            "input": "\u201c计算结果是多少\uff1f\u201d他问道\u3002",
            "expected": '"计算结果是多少?"他问道.'
        },
        {
            "name": "复杂数学表达式",
            "input": "计算\uff1a\uff08x\u2081\uff0bx\u2082\uff09\u00b2\uff0f\uff08y\u2083\uff0dy\u2084\uff09\uff1d\uff1f",
            "expected": "计算:(x1+x2)2/(y3-y4)=?"
        },
        {
            "name": "分数表达式",
            "input": "3/₇ + 1/₃ = 9/₂₁ + 7/₂₁ = 16/₂₁",
            "expected": "3/7 + 1/3 = 9/21 + 7/21 = 16/21"
        },
        {
            "name": "化学公式",
            "input": "2H₂ + O₂ → 2H₂O",
            "expected": "2H2 + O2 → 2H2O"
        }
    ]
    
    print("🔍 测试Unicode字符标准化功能")
    print("=" * 80)
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: {case['name']}")
        print("-" * 50)
        
        # 标准化处理
        result = processor.normalize_unicode_text(case['input'])
        
        print(f"输入: {case['input']}")
        print(f"输出: {result}")
        print(f"期望: {case['expected']}")
        
        if result == case['expected']:
            print("✅ 测试通过")
            success_count += 1
        else:
            print("❌ 测试失败")
            # 显示字符差异
            print("字符差异分析:")
            for j, (actual, expected) in enumerate(zip(result, case['expected'])):
                if actual != expected:
                    print(f"  位置 {j}: 实际='{actual}' (U+{ord(actual):04X}), 期望='{expected}' (U+{ord(expected):04X})")
    
    print("\n" + "=" * 80)
    print(f"📊 测试结果统计:")
    print(f"   总测试用例: {total_count}")
    print(f"   通过: {success_count}")
    print(f"   失败: {total_count - success_count}")
    print(f"   成功率: {success_count/total_count*100:.1f}%")

def test_complete_parsing_with_unicode():
    """测试完整的解析流程（包含Unicode处理）"""
    print("\n" + "=" * 80)
    print("🔍 测试完整解析流程（包含Unicode处理）")
    print("=" * 80)
    
    processor = DifyLabelProcessor(
        dify_api_key="test",
        workflow_id="test",
        input_json_path="test.json",
        output_json_path="test_output.json"
    )
    
    # 模拟Dify输出（包含Unicode字符）
    dify_outputs = [
        {
            "name": "JSON格式含Unicode",
            "output": '{"example_questions": ["1、计算：3/₇ + 2/₅ = ?", "2、化学式：H₂O + CO₂", "3、数学：（x₁＋x₂）² = ?"]}',
        },
        {
            "name": "文本格式含Unicode",
            "output": '''1、小明有₅个苹果，吃了₂个，还剩几个？
2、计算：₃×₄＋₂÷₁＝？
3、分数：₁/₃ ＋ ₂/₅ ＝ ？''',
        },
        {
            "name": "数组格式含Unicode",
            "output": '["1、面积：长₅厘米×宽₃厘米＝？", "2、体积：₂³＝？", "3、百分比：₂₅％＝？"]',
        }
    ]
    
    for i, case in enumerate(dify_outputs, 1):
        print(f"\n完整测试 {i}: {case['name']}")
        print("-" * 50)
        
        result = processor.parse_dify_output(case['output'], "测试标签路径")
        questions = result.get('example_questions', [])
        
        print(f"原始输出: {case['output']}")
        print(f"解析出的问题数量: {len(questions)}")
        print("标准化后的问题:")
        
        for j, q in enumerate(questions, 1):
            print(f"  {j}. {q}")
        
        # 检查是否还有Unicode特殊字符
        has_unicode_issues = False
        for q in questions:
            if any(char in q for char in '₀₁₂₃₄₅₆₇₈₉⁰¹²³⁴⁵⁶⁷⁸⁹'):
                has_unicode_issues = True
                break
        
        if has_unicode_issues:
            print("❌ 仍然包含Unicode特殊字符")
        else:
            print("✅ Unicode字符已正确标准化")

def show_unicode_examples():
    """显示常见的Unicode字符示例"""
    print("\n" + "=" * 80)
    print("📚 常见Unicode字符示例")
    print("=" * 80)
    
    examples = [
        ("下标数字", "₀₁₂₃₄₅₆₇₈₉", "0123456789"),
        ("上标数字", "⁰¹²³⁴⁵⁶⁷⁸⁹", "0123456789"),
        ("数学符号", "×÷−", "*/-"),
        ("全角符号", "＋－＊／＝（）", "+-*/=()"),
        ("全角标点", "，。？！：；", ",.?!:;"),
        ("引号", "\u201c\u201d\u2018\u2019", '""\'\''),
    ]
    
    for name, unicode_chars, normal_chars in examples:
        print(f"\n{name}:")
        print(f"  Unicode: {unicode_chars}")
        print(f"  标准化:   {normal_chars}")
        print(f"  字符码:")
        for i, char in enumerate(unicode_chars):
            print(f"    {char} → U+{ord(char):04X}")

if __name__ == "__main__":
    test_unicode_normalization()
    test_complete_parsing_with_unicode()
    show_unicode_examples()
