#!/usr/bin/env python3
"""
快速去重脚本 - 一键解决你的数据重复问题
"""

import json
import re

def quick_dedupe(input_file: str, output_file: str = None):
    """
    快速去重函数
    
    Args:
        input_file: 输入文件路径
        output_file: 输出文件路径（如果不指定，会自动生成）
    """
    
    if output_file is None:
        base_name = input_file.rsplit('.', 1)[0]
        output_file = f"{base_name}_deduplicated.json"
    
    # 加载数据
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"原始数据: {len(data)} 条")
    
    # 提取题目并去重
    seen = {}
    unique_data = []
    
    for i, item in enumerate(data):
        doc_token = item['doc_token']
        
        # 提取题目部分（在"___"之前）
        if '___' in doc_token:
            question = doc_token.split('___')[0].strip()
        else:
            # 如果没有"___"，尝试其他分隔符
            for sep in ['首先', '解：', '分析：']:
                if sep in doc_token:
                    question = doc_token.split(sep)[0].strip()
                    break
            else:
                question = doc_token[:100]  # 取前100个字符作为题目
        
        # 标准化：移除多余空格
        question = re.sub(r'\s+', ' ', question.strip())
        
        # 检查是否重复
        if question in seen:
            # 重复了，选择更完整的版本（更长的doc_token）
            original_idx = seen[question]
            if len(doc_token) > len(data[original_idx]['doc_token']):
                # 当前版本更完整，替换
                for j, unique_item in enumerate(unique_data):
                    if unique_item == data[original_idx]:
                        unique_data[j] = item
                        break
                seen[question] = i
            # 否则保留原版本，不做任何操作
        else:
            # 新题目
            seen[question] = i
            unique_data.append(item)
    
    # 保存结果
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(unique_data, f, ensure_ascii=False, indent=2)
    
    removed = len(data) - len(unique_data)
    print(f"去重后: {len(unique_data)} 条")
    print(f"移除重复: {removed} 条 ({removed/len(data)*100:.1f}%)")
    print(f"结果保存到: {output_file}")

if __name__ == "__main__":
    # 直接使用 - 只需要修改这里的文件路径
    input_file = "your_data.json"  # 改为你的输入文件路径
    
    quick_dedupe(input_file)
