# 交互式去重使用说明

## 🎯 功能概述

交互式去重功能允许你逐个审核每组重复数据，手动决定是否删除，确保去重的准确性。

## 🚀 使用方法

### 1. 自动模式（默认）
```bash
python label_grouped_deduplication.py
```
- 自动删除所有检测到的重复项
- 适合大批量处理

### 2. 交互模式
```bash
python label_grouped_deduplication.py --interactive
```
- 逐个确认每组重复项
- 适合精确控制去重过程

### 3. 演示模式
```bash
python demo_interactive.py
```
- 使用预设的演示数据
- 体验交互功能

## 🔍 交互界面说明

当发现重复项时，系统会显示：

```
================================================================================
🔍 发现疑似重复项，请审核：
================================================================================
📋 共同前缀: '按一定比放大后长是36厘米宽是18厘米它'
🏷️  标签: 小学数学新知识树 -> 数与代数 -> 比和比例 -> 比例 -> 图形的放大与缩小

📄 项目1 (将被保留):
----------------------------------------
按一定比放大后长是36厘米宽是18厘米它是按( )( )放大 设原长为$L$原宽为$W$...
[总长度: 272 字符]

📄 项目2 (候选删除):
----------------------------------------
按一定比放大后长是36厘米宽是18厘米它是按（ ） （ ）放大 设原长为$l_0$原宽为$w_0$...
[总长度: 246 字符]

📊 长度差异: 272 vs 246 字符
================================================================================
❓ 是否删除项目2？(y=删除, n=保留, s=显示完整内容, q=退出交互模式):
```

## ⌨️ 操作指令

| 指令 | 功能 | 说明 |
|------|------|------|
| `y` | 删除 | 删除项目2，保留项目1 |
| `n` | 保留 | 保留两个项目，不删除 |
| `s` | 显示 | 显示两个项目的完整内容 |
| `q` | 退出 | 退出交互模式，后续自动处理 |

## 📊 去重逻辑

### 前缀匹配算法
- **前缀长度**: 默认20个字符
- **匹配方式**: 精确匹配标准化后的前缀
- **标准化处理**: 
  - 统一括号类型（全角→半角）
  - 移除多余空格
  - 统一数学符号

### 分组策略
1. 按 `doc_label` 分组
2. 在同一标签组内比较前缀
3. 前缀相同的项目被认为是重复

## 📁 输出文件

### 1. 去重结果
- **文件名**: `label_grouped_deduplicated.json`
- **内容**: 去重后的数据

### 2. 审核记录（JSON）
- **文件名**: `label_grouped_deduplicated_duplicate_review.json`
- **内容**: 详细的重复项信息，包含用户决策

### 3. 审核报告（HTML）
- **文件名**: `label_grouped_deduplicated_duplicate_review.html`
- **内容**: 可视化的审核报告，可在浏览器中查看

## ⚙️ 参数配置

在 `main()` 函数中可以调整：

```python
def main(interactive_mode: bool = False):
    input_file = "merge.json"           # 输入文件
    output_file = "output.json"         # 输出文件
    prefix_length = 20                  # 前缀长度
    threshold = 0.999                   # 相似度阈值（兼容性）
```

### 推荐配置
- **前缀长度**: 15-25字符
  - 15字符: 更宽松，可能匹配更多相似题目
  - 20字符: 平衡准确性和覆盖度（推荐）
  - 25字符: 更严格，减少误匹配

## 💡 使用建议

### 1. 首次使用
- 先用演示模式熟悉界面
- 使用小数据集测试

### 2. 大数据集处理
- 先用自动模式快速去重
- 对关键数据使用交互模式精确控制

### 3. 质量控制
- 定期检查HTML审核报告
- 关注用户决策统计

## 🔧 故障排除

### 常见问题

1. **前缀太短，误匹配过多**
   - 增加 `prefix_length` 参数

2. **前缀太长，漏掉真正的重复**
   - 减少 `prefix_length` 参数

3. **交互过程中想批量处理**
   - 输入 `q` 退出交互模式

4. **想重新审核某些项目**
   - 查看JSON审核记录
   - 手动恢复需要的数据

## 📈 效果评估

### 统计指标
- 原始数据量
- 去重后数据量
- 去重率
- 用户确认的删除数量
- 用户保留的疑似重复数量

### 质量检查
- 检查HTML报告中的重复项
- 验证保留的数据是否合理
- 确认删除的数据确实重复

## 🎯 最佳实践

1. **分批处理**: 大数据集分批进行交互审核
2. **备份数据**: 处理前备份原始数据
3. **记录决策**: 保存审核记录用于后续分析
4. **定期优化**: 根据审核结果调整参数
