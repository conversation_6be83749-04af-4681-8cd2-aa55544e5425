import os
import json
import requests
import logging
import time
import concurrent.futures
from threading import Lock
from tqdm import tqdm
from typing import List, Dict, Optional
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from pathlib import Path
# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("processing.log"),
        logging.StreamHandler()
    ]
)

class DifyImageProcessor:
    def __init__(self, dify_api_key: str, workflow_id: str,
                 json_path: str = r"D:\python_project\dify-图片转解析\math1_questions_point_single_2501-2507.json",
                 image_dir: str = r"D:\python_project\dify-图片转解析\图片示例",
                 result_dir: str = "results",
                 dify_base_url: str = "https://dify.xmdas-link.com"):  # 修改为你的Dify部署地址
        """
        初始化Dify图片处理器，新增dify_base_url参数适配自定义部署
        """
        self.dify_api_key = dify_api_key
        self.workflow_id = workflow_id
        self.json_path = json_path
        self.image_dir = image_dir
        self.result_dir = result_dir
        self.dify_base_url = dify_base_url  # 自定义Dify基础地址
        
        os.makedirs(self.result_dir, exist_ok=True)
        
        # 调整API地址为基于基础URL的路径
        # 使用正确的API端点
        self.dify_workflow_url = f"{self.dify_base_url}/v1/workflows/run"
        self.dify_parameters_url = f"{self.dify_base_url}/v1/parameters"
        self.upload_url = f"{self.dify_base_url}/v1/files/upload"  # 上传地址基于基础URL
        self.headers = {
            "Authorization": f"Bearer {self.dify_api_key}",
            "Content-Type": "application/json"
        }
        
        self.supported_formats = ['.jpg', '.jpeg', '.png', '.gif', '.bmp']

        # 高性能优化
        self.session = self.create_optimized_session()
        self.stats_lock = Lock()
        self.stats = {"success": 0, "failed": 0, "skipped": 0}

        # 合并结果存储
        self.results_lock = Lock()
        self.all_results = []
        self.final_json_path = os.path.join(self.result_dir, "all_results.json")

        # 加载已存在的结果（支持断点续传）
        self.load_existing_results()

    def find_empty_fields(self) -> List[str]:
        """查找需要重新处理的question_id（空answer、空analysis或analysis包含嵌套JSON）"""
        empty_ids = []

        for item in self.all_results:
            question_id = item.get('question_id', '')
            answer = item.get('answer', '')
            analysis = item.get('analysis', '')

            # 检查空answer或空analysis
            if not answer or not analysis:
                empty_ids.append(question_id)
                continue

            # 检查analysis是否包含嵌套JSON（无论answer是否为空）
            if analysis.startswith('{"answer"') or analysis.startswith('{\n    "answer"'):
                empty_ids.append(question_id)
                continue

        return empty_ids

    def fix_nested_json_fields(self, item: dict) -> bool:
        """修复嵌套JSON字段，返回是否修复成功"""
        analysis = item.get('analysis', '')
        current_answer = item.get('answer', '')

        # 如果analysis包含嵌套的JSON
        if analysis.startswith('{"answer"') or analysis.startswith('{\n    "answer"'):
            try:
                # 尝试解析嵌套的JSON
                nested_data = json.loads(analysis)

                # 如果当前answer为空，则使用嵌套JSON中的answer
                if not current_answer and nested_data.get('answer'):
                    item['answer'] = nested_data['answer']

                # 总是使用嵌套JSON中的analysis来替换原始的嵌套JSON字符串
                if nested_data.get('analysis'):
                    item['analysis'] = nested_data['analysis']
                    return True

            except json.JSONDecodeError:
                # 尝试手动提取
                try:
                    # 如果当前answer为空，尝试提取answer
                    if not current_answer:
                        patterns = ['"answer": "', '"answer":"']
                        for pattern in patterns:
                            if pattern in analysis:
                                start = analysis.find(pattern) + len(pattern)
                                # 找到结束位置
                                ends = []
                                for end_pattern in ['","', '",\n', '"}']:
                                    pos = analysis.find(end_pattern, start)
                                    if pos != -1:
                                        ends.append(pos)

                                if ends:
                                    end = min(ends)
                                    answer = analysis[start:end]
                                    item['answer'] = answer
                                    break

                    # 总是尝试提取并清理analysis
                    analysis_patterns = ['"analysis": "', '"analysis":"']
                    for a_pattern in analysis_patterns:
                        if a_pattern in analysis:
                            a_start = analysis.find(a_pattern) + len(a_pattern)
                            a_end = analysis.rfind('"}')
                            if a_end > a_start:
                                clean_analysis = analysis[a_start:a_end]
                                # 处理转义字符
                                clean_analysis = clean_analysis.replace('\\"', '"').replace('\\\\', '\\')
                                item['analysis'] = clean_analysis
                                return True
                            break
                except Exception:
                    pass

        return False

    def reprocess_empty_fields(self, max_workers: int = 4, batch_size: int = 20):
        """重新处理空字段的数据"""
        print("🔍 正在查找需要重新处理的数据...")

        # 首先尝试修复嵌套JSON
        fixed_count = 0
        for item in self.all_results:
            if self.fix_nested_json_fields(item):
                fixed_count += 1

        if fixed_count > 0:
            print(f"✅ 修复了 {fixed_count} 个嵌套JSON字段")
            self.save_final_results()

        # 查找仍然需要重新处理的ID
        empty_ids = self.find_empty_fields()

        if not empty_ids:
            print("🎉 没有发现需要重新处理的数据！")
            return

        print(f"📋 发现 {len(empty_ids)} 个需要重新处理的数据")

        # 从all_results中移除这些需要重新处理的项目
        original_count = len(self.all_results)
        self.all_results = [item for item in self.all_results
                           if item.get('question_id') not in empty_ids]
        removed_count = original_count - len(self.all_results)
        print(f"🗑️  从结果中移除了 {removed_count} 个待重新处理的项目")

        # 重新处理这些ID
        print(f"🚀 开始重新处理 {len(empty_ids)} 个数据 (线程数: {max_workers}, 批次: {batch_size})")

        start_time = time.time()
        total_ids = len(empty_ids)

        # 分批处理
        for batch_start in range(0, total_ids, batch_size):
            batch_end = min(batch_start + batch_size, total_ids)
            batch_ids = empty_ids[batch_start:batch_end]

            with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_id = {
                    executor.submit(self.process_single_id_fast, id): id
                    for id in batch_ids
                }

                with tqdm(total=len(batch_ids), desc=f"重新处理批次 {batch_start//batch_size + 1}/{(total_ids-1)//batch_size + 1}") as pbar:
                    for future in concurrent.futures.as_completed(future_to_id):
                        try:
                            result = future.result()
                        except Exception:
                            self.update_stats("failed")
                        pbar.update(1)

            # 每个批次完成后保存一次结果
            self.save_final_results()

            # 批次间短暂休息
            if batch_end < total_ids:
                time.sleep(1)

        elapsed_time = time.time() - start_time

        print(f"\n📈 重新处理完成统计:")
        print(f"   总数量: {total_ids}")
        print(f"   成功: {self.stats['success']}")
        print(f"   失败: {self.stats['failed']}")
        print(f"   跳过: {self.stats['skipped']}")
        print(f"   耗时: {elapsed_time:.1f}秒")
        print(f"   速度: {total_ids/elapsed_time:.1f}个/秒")

        print(f"\n✅ 重新处理结果已保存到: {self.final_json_path}")

    def fix_all_nested_json(self):
        """一次性修复所有嵌套JSON问题，不重新调用API"""
        print("🔧 开始修复所有嵌套JSON问题...")

        fixed_count = 0
        total_count = len(self.all_results)

        for i, item in enumerate(self.all_results):
            if self.fix_nested_json_fields(item):
                fixed_count += 1
                if fixed_count % 100 == 0:  # 每修复100个显示一次进度
                    print(f"📊 已修复 {fixed_count} 个，进度: {i+1}/{total_count}")

        if fixed_count > 0:
            print(f"✅ 共修复了 {fixed_count} 个嵌套JSON字段")
            self.save_final_results()
            print(f"💾 修复结果已保存到: {self.final_json_path}")
        else:
            print("ℹ️  没有发现需要修复的嵌套JSON字段")

        return fixed_count

    def load_existing_results(self) -> None:
        """从合并的JSON文件加载已存在的结果，支持断点续传"""
        try:
            if os.path.exists(self.final_json_path):
                with open(self.final_json_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if isinstance(data, dict) and 'results' in data:
                        self.all_results = data['results']
                        print(f"已加载 {len(self.all_results)} 个已存在的结果")

        except Exception as e:
            print(f"加载已存在结果时发生错误: {str(e)}")

    def create_optimized_session(self) -> requests.Session:
        """创建优化的HTTP会话，支持连接池和重试"""
        session = requests.Session()

        # 重试策略
        retry_strategy = Retry(
            total=3,
            backoff_factor=0.5,
            status_forcelist=[429, 500, 502, 503, 504],
        )

        # 连接池配置
        adapter = HTTPAdapter(
            pool_connections=50,
            pool_maxsize=50,
            max_retries=retry_strategy
        )

        session.mount("http://", adapter)
        session.mount("https://", adapter)
        return session

    def update_stats(self, result_type: str) -> None:
        """线程安全的统计更新"""
        with self.stats_lock:
            self.stats[result_type] += 1

    def get_workflow_parameters(self) -> Optional[dict]:
        """获取工作流参数信息"""
        try:
            response = requests.get(
                self.dify_parameters_url,
                headers=self.headers
            )
            response.raise_for_status()
            result = response.json()
            logging.info(f"工作流参数: {json.dumps(result, indent=2)}")
            return result
        except Exception as e:
            logging.error(f"获取工作流参数失败: {str(e)}")
            return None

    def read_question_ids(self) -> List[str]:
        image_dir = "D:\\python_project\\dify-图片转解析\\question_images_removed"
        image_files = [f for f in os.listdir(image_dir) if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
        image_ids = [int(Path(f).stem) for f in image_files]
        return image_ids

    def find_image_file(self, id: str) -> Optional[str]:
        # 保持不变
        for ext in self.supported_formats:
            image_path = os.path.join(self.image_dir, f"{id}{ext}")
            if os.path.exists(image_path) and os.path.isfile(image_path):
                return image_path
        return None

    def upload_image_to_dify(self, image_path: str) -> Optional[str]:
        """优化的图片上传方法，使用会话和流式处理"""
        try:
            upload_headers = {
                "Authorization": f"Bearer {self.dify_api_key}"
            }

            # 使用流式上传，减少内存占用
            with open(image_path, "rb") as f:
                files = {
                    "file": (
                        os.path.basename(image_path),
                        f,  # 直接传递文件对象
                        f"image/{os.path.splitext(image_path)[1][1:]}"
                    )
                }

                # 使用优化的session
                response = self.session.post(
                    self.upload_url,
                    headers=upload_headers,
                    files=files,
                    data={"purpose": "vision"}
                )
            
            # 检查响应状态
            if response.status_code == 405:
                # 尝试替换上传路径（某些部署可能使用不同端点）
                alt_upload_url = f"{self.dify_base_url}/api/v1/files/upload"
                with open(image_path, "rb") as f:
                    files = {
                        "file": (
                            os.path.basename(image_path),
                            f,
                            f"image/{os.path.splitext(image_path)[1][1:]}"
                        )
                    }
                    response = self.session.post(
                        alt_upload_url,
                        headers=upload_headers,
                        files=files,
                        data={"purpose": "vision"}
                    )

            if not response.text.strip():
                return None

            response.raise_for_status()  # 抛出HTTP错误

            try:
                result = response.json()
            except json.JSONDecodeError:
                return None

            file_id = result.get("id") or result.get("file_id")
            return file_id
            
        except Exception as e:
            logging.error(f"上传图片失败 {image_path}: {str(e)}")
            return None

    def run_dify_workflow(self, file_id: str) -> Optional[str]:
        """调用工作流，使用上传的文件ID"""
        try:
            # 根据工作流参数，使用正确的格式
            payload = {
                "inputs": {
                    "image": {
                        "transfer_method": "local_file",
                        "upload_file_id": file_id,
                        "type": "image"
                    }
                },
                "response_mode": "blocking",
                "user": "image_processor_script",
                "workflow": self.workflow_id
            }

            response = self.session.post(
                self.dify_workflow_url,
                headers=self.headers,
                json=payload
            )

            # 先检查响应内容是否为空
            if not response.text.strip():
                return None

            response.raise_for_status()

            try:
                result = response.json()
            except json.JSONDecodeError:
                return None

            if "data" in result and "outputs" in result["data"]:
                outputs = result["data"]["outputs"]
                if "output" in outputs:
                    return str(outputs["output"])
                else:
                    return str(outputs)
            elif "output" in result:
                return str(result["output"])
            else:
                return None

        except Exception:
            return None

    def save_result(self, id: str, result: str) -> None:
        """只添加到总结果中，不保存单个文件"""
        try:
            # 解析工作流返回的结果
            parsed_result = self.parse_workflow_result(result, id)

            # 添加到总结果中（线程安全，避免重复）
            with self.results_lock:
                # 检查是否已存在相同的question_id
                existing_ids = {item['question_id'] for item in self.all_results}
                if id not in existing_ids:
                    self.all_results.append(parsed_result)

        except Exception:
            pass

    def save_final_results(self) -> None:
        """保存所有结果到一个JSON文件"""
        try:
            with self.results_lock:
                if not self.all_results:
                    logging.warning("没有结果需要保存")
                    return

                # 按question_id排序
                sorted_results = sorted(self.all_results, key=lambda x: x['question_id'])

                # 创建最终的JSON结构
                final_data = {
                    "total_count": len(sorted_results),
                    "processed_time": time.strftime("%Y-%m-%d %H:%M:%S"),
                    "results": sorted_results
                }

                # 保存到文件
                with open(self.final_json_path, 'w', encoding='utf-8') as f:
                    json.dump(final_data, f, ensure_ascii=False, indent=2)

                # 只在最终完成时显示详细信息，批次更新时简化显示
                file_size = os.path.getsize(self.final_json_path) / 1024  # KB
                print(f"� 已更新: {self.final_json_path} ({len(sorted_results)}个结果, {file_size:.1f}KB)")

        except Exception as e:
            print(f"❌ 保存最终结果失败: {str(e)}")

    def parse_workflow_result(self, result: str, question_id: str) -> dict:
        """解析工作流结果，提取answer和analysis"""
        try:
            output_str = result

            # 如果包含JSON代码块，提取其中的JSON内容
            if "```json" in output_str:
                # 提取```json和```之间的内容
                start_marker = "```json"
                end_marker = "```"
                start_idx = output_str.find(start_marker)
                if start_idx != -1:
                    start_idx += len(start_marker)
                    end_idx = output_str.find(end_marker, start_idx)
                    if end_idx != -1:
                        json_content = output_str[start_idx:end_idx].strip()
                        # 移除可能的前导换行符和空白字符
                        json_content = json_content.lstrip('\n\r\t ')
                        try:
                            output_data = json.loads(json_content)
                        except json.JSONDecodeError:
                            output_data = {"answer": "", "analysis": json_content}
                    else:
                        output_data = {"answer": "", "analysis": output_str}
                else:
                    output_data = {"answer": "", "analysis": output_str}
            else:
                # 尝试直接解析JSON字符串
                try:
                    # 首先尝试直接解析
                    output_data = json.loads(output_str)
                except json.JSONDecodeError:
                    # 如果失败，检查是否是嵌套的字符串格式
                    if output_str.startswith("{'output'"):
                        try:
                            # 使用eval解析字符串形式的字典
                            temp_dict = eval(output_str)
                            inner_json = temp_dict.get('output', '{}')
                            output_data = json.loads(inner_json)
                        except:
                            output_data = {"answer": "", "analysis": output_str}
                    elif output_str.startswith('{"answer"'):
                        # 处理直接的JSON字符串格式
                        try:
                            output_data = json.loads(output_str)
                        except:
                            output_data = {"answer": "", "analysis": output_str}
                    else:
                        output_data = {"answer": "", "analysis": output_str}

            # 确保提取的是干净的数据，不包含嵌套JSON
            if isinstance(output_data, dict):
                # 如果answer为空但analysis包含JSON，尝试提取
                if not output_data.get('answer') and output_data.get('analysis'):
                    analysis = output_data['analysis']
                    if isinstance(analysis, str) and (analysis.startswith('{"answer"') or analysis.startswith('{\n    "answer"')):
                        try:
                            nested_data = json.loads(analysis)
                            if nested_data.get('answer'):
                                output_data['answer'] = nested_data['answer']
                            if nested_data.get('analysis'):
                                output_data['analysis'] = nested_data['analysis']
                        except:
                            pass

            # 构造最终结果
            final_result = {
                "question_id": question_id,
                "answer": output_data.get("answer", ""),
                "analysis": output_data.get("analysis", "")
            }

            return final_result

        except Exception as e:
            logging.error(f"解析工作流结果失败: {str(e)}")
            # 返回默认结构
            return {
                "question_id": question_id,
                "answer": "",
                "analysis": str(result)
            }

    def process_single_id(self, id: str) -> bool:
        """原始的单个处理方法（保持兼容性）"""
        return self.process_single_id_fast(id) == "success"

    def process_single_id_fast(self, id: str) -> str:
        """高性能单个处理方法，返回处理状态"""
        try:
            # 检查是否已在内存中的结果列表中
            with self.results_lock:
                existing_ids = {item['question_id'] for item in self.all_results}
                if id in existing_ids:
                    self.update_stats("skipped")
                    return "skipped"

            image_path = self.find_image_file(id)
            if not image_path:
                self.update_stats("failed")
                return "failed"

            # 先上传文件获取file_id
            file_id = self.upload_image_to_dify(image_path)
            if not file_id:
                self.update_stats("failed")
                return "failed"

            # 使用file_id调用工作流
            result = self.run_dify_workflow(file_id)
            if not result:
                self.update_stats("failed")
                return "failed"

            self.save_result(id, result)
            self.update_stats("success")
            return "success"

        except Exception as e:
            self.update_stats("failed")
            return "failed"

    def process_all_ids(self) -> None:
        """原始的单线程处理方法（保持兼容性）"""
        try:
            ids = self.read_question_ids()
            if not ids:
                logging.warning("没有找到可处理的question_id")
                return

            success_count = 0
            for id in tqdm(ids, desc="处理进度"):
                if self.process_single_id(id):
                    success_count += 1

            print(f"\n📈 处理完成 - 总数量: {len(ids)}, 成功: {success_count}, 失败: {len(ids) - success_count}")

            # 保存最终合并的JSON文件
            self.save_final_results()

        except Exception as e:
            logging.error(f"批量处理时发生错误: {str(e)}")

    def process_concurrent(self, max_workers: int = 8, batch_size: int = 5000) -> None:
        """高性能并发处理方法"""
        ids = self.read_question_ids()
        if not ids:
            logging.warning("没有找到可处理的question_id")
            return
        # ids = ids[35000:]
        total_ids = len(ids)
        print(f"🚀 开始处理 {total_ids} 个图片 (线程数: {max_workers}, 批次: {batch_size})")

        start_time = time.time()

        failed_ids = []  # ⬅️ 用于记录失败的ID

        # 分批处理，避免内存问题和API限流
        for batch_start in range(0, total_ids, batch_size):
            batch_end = min(batch_start + batch_size, total_ids)
            batch_ids = ids[batch_start:batch_end]

            with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_id = {
                    executor.submit(self.process_single_id_fast, id): id
                    for id in batch_ids
                }

                with tqdm(total=len(batch_ids), desc=f"批次 {batch_start//batch_size + 1}/{(total_ids-1)//batch_size + 1}") as pbar:
                    for future in concurrent.futures.as_completed(future_to_id):
                        id = future_to_id[future]
                        try:
                            result = future.result()
                        except Exception as e:
                            self.update_stats("failed")
                            failed_ids.append(id)  # ⬅️ 记录失败的ID
                        pbar.update(1)

            # 每个批次完成后保存一次结果
            self.save_final_results()

            # 批次间短暂休息，避免API限流
            if batch_end < total_ids:
                time.sleep(1)

        elapsed_time = time.time() - start_time

        print(f"\n📈 处理完成统计:")
        print(f"   总数量: {total_ids}")
        print(f"   成功: {self.stats['success']}")
        print(f"   失败: {self.stats['failed']}")
        print(f"   跳过: {self.stats['skipped']}")
        print(f"   耗时: {elapsed_time:.1f}秒")
        print(f"   速度: {total_ids/elapsed_time:.1f}个/秒")

        # 写入失败的 ID 到文件
        if failed_ids:
            failed_path = "failed_ids.txt"
            with open(failed_path, "w", encoding="utf-8") as f:
                for fid in failed_ids:
                    f.write(f"{fid}\n")
            print(f"\n❗ 共 {len(failed_ids)} 个处理失败的ID已写入 {failed_path}")

        print(f"\n✅ 最终结果已保存到: {self.final_json_path}")


if __name__ == "__main__":
    # 填写你的实际参数
    DIFY_API_KEY = "app-m34jaS5NMuIT5yGViXWyq0g1"
    WORKFLOW_ID = "vKDOeCan7OaTb3u0"
    DIFY_BASE_URL = "https://dify.xmdas-link.com"  # 你的Dify部署基础地址

    processor = DifyImageProcessor(
        dify_api_key=DIFY_API_KEY,
        workflow_id=WORKFLOW_ID,
        json_path="D:\\python_project\\dify-图片转解析\\math1_questions_point_single_2501-2507.json",
        image_dir="D:\\python_project\\dify-图片转解析\\question_images_removed",
        result_dir="results",
        dify_base_url=DIFY_BASE_URL
    )

    # max_workers = 8  # 并发线程数，可根据需要调整
    # batch_size = 20  # 批次大小，可根据内存情况调整

    # processor.process_concurrent(max_workers=max_workers, batch_size=batch_size)


    # 选择处理模式
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "--reprocess":
        # 重新处理空字段模式
        max_workers = 4  # 并发线程数
        batch_size = 20  # 批次大小

        if len(sys.argv) > 2:
            max_workers = int(sys.argv[2])
        if len(sys.argv) > 3:
            batch_size = int(sys.argv[3])

        print(f"🔄 启动重新处理模式: {max_workers} 线程, 批次大小: {batch_size}")
        processor.reprocess_empty_fields(max_workers=max_workers, batch_size=batch_size)
    elif len(sys.argv) > 1 and sys.argv[1] == "--fix-nested":
        # 修复嵌套JSON模式
        print("🔧 启动嵌套JSON修复模式")
        analysis_processor = AnalysisProcessor()
        analysis_processor.fix_all_nested_json()
    elif len(sys.argv) > 1 and sys.argv[1] == "--fast":
        # 高性能模式
        max_workers = 20  # 并发线程数，可根据需要调整
        batch_size = 50  # 批次大小，可根据内存情况调整

        if len(sys.argv) > 2:
            max_workers = int(sys.argv[2])
        if len(sys.argv) > 3:
            batch_size = int(sys.argv[3])

        print(f"🚀 启动高性能模式: {max_workers} 线程, 批次大小: {batch_size}")
        processor.process_concurrent(max_workers=max_workers, batch_size=batch_size)
    else:
        # 传统单线程模式
        print("🐌 启动传统单线程模式")
        print("💡 提示:")
        print("   使用 --fast 参数启动高性能模式: python get_analysis.py --fast 8 50")
        print("   使用 --reprocess 参数重新处理空字段: python get_analysis.py --reprocess 4 20")
        print("   使用 --fix-nested 参数修复嵌套JSON: python get_analysis.py --fix-nested")
        processor.process_all_ids()
    