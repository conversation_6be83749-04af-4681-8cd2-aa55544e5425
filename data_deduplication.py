#!/usr/bin/env python3
"""
数据去重脚本 - 处理doc_token中的重复内容
支持多种去重策略
"""

import json
import re
from typing import List, Dict, Set, Tuple
from collections import defaultdict
import hashlib

class DataDeduplicator:
    def __init__(self, input_file: str, output_file: str):
        self.input_file = input_file
        self.output_file = output_file
        self.data = []
        self.duplicates_info = []
    
    def load_data(self) -> List[Dict]:
        """加载JSON数据"""
        try:
            with open(self.input_file, 'r', encoding='utf-8') as f:
                self.data = json.load(f)
            print(f"✅ 成功加载 {len(self.data)} 条数据")
            return self.data
        except Exception as e:
            print(f"❌ 加载数据失败: {str(e)}")
            return []
    
    def normalize_text(self, text: str) -> str:
        """标准化文本 - 移除多余空格、标点符号等"""
        # 移除多余的空格
        text = re.sub(r'\s+', ' ', text.strip())
        
        # 移除特殊字符（保留中文、数字、基本标点）
        text = re.sub(r'[^\u4e00-\u9fff\w\s\.\,\?\!\(\)\[\]\{\}\+\-\*\/\=\<\>\$\\\^\_\~\|]', '', text)
        
        # 统一数学符号
        text = text.replace('×', '*').replace('÷', '/')
        text = text.replace('＞', '>').replace('＜', '<').replace('＝', '=')
        
        return text.strip()
    
    def extract_question_part(self, doc_token: str) -> str:
        """提取题目部分（去除解析部分）"""
        # 常见的分隔符，用于分离题目和解析
        separators = [
            '___',
            '首先',
            '解：',
            '分析：',
            '答案：',
            '解答：',
            '计算过程：'
        ]
        
        question_part = doc_token
        for sep in separators:
            if sep in doc_token:
                question_part = doc_token.split(sep)[0].strip()
                break
        
        return self.normalize_text(question_part)
    
    def method1_exact_question_match(self) -> List[Dict]:
        """方法1：基于题目部分的精确匹配去重"""
        print("\n🔍 方法1：基于题目部分的精确匹配去重")
        
        seen_questions = {}
        unique_data = []
        duplicates = []
        
        for i, item in enumerate(self.data):
            doc_token = item.get('doc_token', '')
            question = self.extract_question_part(doc_token)
            
            if question in seen_questions:
                # 发现重复
                original_idx = seen_questions[question]
                duplicates.append({
                    'question': question,
                    'original_index': original_idx,
                    'duplicate_index': i,
                    'original_length': len(self.data[original_idx]['doc_token']),
                    'duplicate_length': len(doc_token)
                })
                print(f"  发现重复 - 索引 {i} 与索引 {original_idx} 重复")
            else:
                seen_questions[question] = i
                unique_data.append(item)
        
        print(f"✅ 去重完成：原始 {len(self.data)} 条 → 去重后 {len(unique_data)} 条")
        print(f"📊 发现 {len(duplicates)} 组重复数据")
        
        self.duplicates_info = duplicates
        return unique_data
    
    def method2_similarity_based(self, threshold: float = 0.9) -> List[Dict]:
        """方法2：基于相似度的去重"""
        print(f"\n🔍 方法2：基于相似度的去重（阈值: {threshold}）")
        
        def calculate_similarity(text1: str, text2: str) -> float:
            """计算两个文本的相似度（简单的字符级相似度）"""
            if not text1 or not text2:
                return 0.0
            
            # 使用最长公共子序列的思想
            len1, len2 = len(text1), len(text2)
            if len1 == 0 or len2 == 0:
                return 0.0
            
            # 简单的字符匹配相似度
            common_chars = sum(1 for c1, c2 in zip(text1, text2) if c1 == c2)
            max_len = max(len1, len2)
            return common_chars / max_len
        
        unique_data = []
        processed_indices = set()
        
        for i, item in enumerate(self.data):
            if i in processed_indices:
                continue
            
            question1 = self.extract_question_part(item.get('doc_token', ''))
            similar_items = [i]
            
            # 查找相似的项目
            for j in range(i + 1, len(self.data)):
                if j in processed_indices:
                    continue
                
                question2 = self.extract_question_part(self.data[j].get('doc_token', ''))
                similarity = calculate_similarity(question1, question2)
                
                if similarity >= threshold:
                    similar_items.append(j)
                    processed_indices.add(j)
                    print(f"  发现相似项 - 索引 {i} 与索引 {j} 相似度: {similarity:.3f}")
            
            # 选择最完整的版本（通常是最长的）
            best_item = max(similar_items, key=lambda idx: len(self.data[idx]['doc_token']))
            unique_data.append(self.data[best_item])
            processed_indices.add(i)
        
        print(f"✅ 去重完成：原始 {len(self.data)} 条 → 去重后 {len(unique_data)} 条")
        return unique_data
    
    def method3_hash_based(self) -> List[Dict]:
        """方法3：基于哈希的去重（最快速）"""
        print("\n🔍 方法3：基于哈希的去重")
        
        seen_hashes = {}
        unique_data = []
        
        for i, item in enumerate(self.data):
            question = self.extract_question_part(item.get('doc_token', ''))
            # 创建哈希
            question_hash = hashlib.md5(question.encode('utf-8')).hexdigest()
            
            if question_hash in seen_hashes:
                original_idx = seen_hashes[question_hash]
                print(f"  发现重复 - 索引 {i} 与索引 {original_idx} 重复")
                
                # 选择更完整的版本
                if len(item['doc_token']) > len(self.data[original_idx]['doc_token']):
                    # 当前项更完整，替换之前的
                    for idx, unique_item in enumerate(unique_data):
                        if unique_item == self.data[original_idx]:
                            unique_data[idx] = item
                            break
                    seen_hashes[question_hash] = i
            else:
                seen_hashes[question_hash] = i
                unique_data.append(item)
        
        print(f"✅ 去重完成：原始 {len(self.data)} 条 → 去重后 {len(unique_data)} 条")
        return unique_data
    
    def method4_advanced_question_extraction(self) -> List[Dict]:
        """方法4：高级题目提取和去重"""
        print("\n🔍 方法4：高级题目提取和去重")
        
        def advanced_extract_question(doc_token: str) -> str:
            """更智能的题目提取"""
            # 移除LaTeX数学符号
            text = re.sub(r'\$[^$]*\$', '', doc_token)
            text = re.sub(r'\\[a-zA-Z]+\{[^}]*\}', '', text)
            text = re.sub(r'\\[a-zA-Z]+', '', text)
            
            # 查找题目结束标志
            end_markers = [
                '___',
                '首先',
                '解：',
                '分析：',
                '答案：',
                '解答：',
                '计算过程：',
                '因此',
                '所以',
                '故'
            ]
            
            question = doc_token
            for marker in end_markers:
                if marker in text:
                    question = text.split(marker)[0].strip()
                    break
            
            # 标准化
            question = self.normalize_text(question)
            
            # 移除题号
            question = re.sub(r'^\d+[、．\.]', '', question).strip()
            
            return question
        
        seen_questions = {}
        unique_data = []
        
        for i, item in enumerate(self.data):
            doc_token = item.get('doc_token', '')
            question = advanced_extract_question(doc_token)
            
            if question in seen_questions:
                original_idx = seen_questions[question]
                print(f"  发现重复 - 索引 {i} 与索引 {original_idx} 重复")
                
                # 选择解析更完整的版本
                original_item = self.data[original_idx]
                if len(doc_token) > len(original_item['doc_token']):
                    # 替换为更完整的版本
                    for idx, unique_item in enumerate(unique_data):
                        if unique_item == original_item:
                            unique_data[idx] = item
                            break
                    seen_questions[question] = i
            else:
                seen_questions[question] = i
                unique_data.append(item)
        
        print(f"✅ 去重完成：原始 {len(self.data)} 条 → 去重后 {len(unique_data)} 条")
        return unique_data
    
    def save_result(self, unique_data: List[Dict], method_name: str = ""):
        """保存去重结果"""
        output_file = self.output_file
        if method_name:
            base_name = self.output_file.rsplit('.', 1)[0]
            ext = self.output_file.rsplit('.', 1)[1] if '.' in self.output_file else 'json'
            output_file = f"{base_name}_{method_name}.{ext}"
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(unique_data, f, ensure_ascii=False, indent=2)
            
            file_size = len(json.dumps(unique_data, ensure_ascii=False)) / 1024
            print(f"💾 结果已保存到: {output_file}")
            print(f"📊 文件大小: {file_size:.1f} KB")
            
        except Exception as e:
            print(f"❌ 保存失败: {str(e)}")
    
    def analyze_duplicates(self):
        """分析重复数据的详细信息"""
        if not self.duplicates_info:
            print("没有重复数据信息可分析")
            return
        
        print(f"\n📊 重复数据分析:")
        print(f"总重复组数: {len(self.duplicates_info)}")
        
        # 按标签分组分析
        label_groups = defaultdict(int)
        for dup in self.duplicates_info:
            original_item = self.data[dup['original_index']]
            label_path = " -> ".join(original_item.get('doc_label', []))
            label_groups[label_path] += 1
        
        print(f"\n重复数据按标签分布:")
        for label, count in sorted(label_groups.items(), key=lambda x: x[1], reverse=True)[:10]:
            print(f"  {label}: {count} 组重复")
    
    def run_all_methods(self):
        """运行所有去重方法并比较结果"""
        if not self.load_data():
            return
        
        print("=" * 80)
        print("数据去重分析")
        print("=" * 80)
        
        methods = [
            ("exact_match", self.method1_exact_question_match),
            ("similarity", lambda: self.method2_similarity_based(0.9)),
            ("hash", self.method3_hash_based),
            ("advanced", self.method4_advanced_question_extraction)
        ]
        
        results = {}
        
        for method_name, method_func in methods:
            print(f"\n{'='*50}")
            result = method_func()
            results[method_name] = result
            self.save_result(result, method_name)
        
        # 比较结果
        print(f"\n{'='*50}")
        print("📊 方法比较:")
        print(f"原始数据: {len(self.data)} 条")
        for method_name, result in results.items():
            reduction = len(self.data) - len(result)
            percentage = (reduction / len(self.data)) * 100
            print(f"{method_name:12}: {len(result):4} 条 (减少 {reduction:3} 条, {percentage:5.1f}%)")
        
        # 分析重复数据
        if hasattr(self, 'duplicates_info'):
            self.analyze_duplicates()


def main():
    """主函数 - 使用示例"""
    # 配置文件路径
    input_file = "your_input_file.json"  # 替换为你的输入文件路径
    output_file = "deduplicated_output.json"  # 输出文件路径
    
    # 创建去重器
    deduplicator = DataDeduplicator(input_file, output_file)
    
    # 运行所有方法
    deduplicator.run_all_methods()
    
    print(f"\n✅ 去重完成！请查看输出文件了解结果。")


if __name__ == "__main__":
    # 如果直接运行此脚本，请修改文件路径
    print("请修改 input_file 和 output_file 路径后运行")
    print("或者导入此模块在其他脚本中使用")
    
    # 取消注释下面的行来运行
    # main()
