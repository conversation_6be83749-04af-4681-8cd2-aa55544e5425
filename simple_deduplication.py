#!/usr/bin/env python3
"""
简化版数据去重脚本 - 专门处理你的数据格式
"""

import json
import re
from typing import List, Dict

def normalize_question(doc_token: str) -> str:
    """
    标准化题目文本，用于去重比较
    """
    # 提取题目部分（在"___"或解析关键词之前的部分）
    separators = ['___', '首先', '解：', '分析：', '答案：', '解答：']
    question = doc_token
    
    for sep in separators:
        if sep in doc_token:
            question = doc_token.split(sep)[0].strip()
            break
    
    # 标准化处理
    # 1. 移除多余空格
    question = re.sub(r'\s+', ' ', question.strip())
    
    # 2. 移除LaTeX数学符号（保留内容）
    question = re.sub(r'\$([^$]*)\$', r'\1', question)
    
    # 3. 统一标点符号
    question = question.replace('×', '*').replace('÷', '/')
    question = question.replace('＞', '>').replace('＜', '<').replace('＝', '=')
    
    # 4. 移除题号（如果有）
    question = re.sub(r'^\d+[、．\.]', '', question).strip()
    
    return question

def deduplicate_data(input_file: str, output_file: str, keep_longest: bool = True):
    """
    去重主函数
    
    Args:
        input_file: 输入JSON文件路径
        output_file: 输出JSON文件路径
        keep_longest: True=保留最长的版本，False=保留第一个遇到的版本
    """
    
    # 加载数据
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"✅ 成功加载 {len(data)} 条数据")
    except Exception as e:
        print(f"❌ 加载数据失败: {str(e)}")
        return
    
    # 去重处理
    seen_questions = {}  # 标准化题目 -> 数据项索引
    unique_data = []
    duplicate_count = 0
    
    for i, item in enumerate(data):
        doc_token = item.get('doc_token', '')
        normalized_question = normalize_question(doc_token)
        
        if normalized_question in seen_questions:
            # 发现重复
            duplicate_count += 1
            original_idx = seen_questions[normalized_question]
            
            print(f"发现重复 #{duplicate_count}:")
            print(f"  原始 (索引 {original_idx}): {data[original_idx]['doc_token'][:100]}...")
            print(f"  重复 (索引 {i}): {doc_token[:100]}...")
            
            if keep_longest:
                # 比较长度，保留更完整的版本
                if len(doc_token) > len(data[original_idx]['doc_token']):
                    # 当前版本更长，替换原来的
                    for j, unique_item in enumerate(unique_data):
                        if unique_item == data[original_idx]:
                            unique_data[j] = item
                            seen_questions[normalized_question] = i
                            print(f"  → 替换为更长的版本")
                            break
                else:
                    print(f"  → 保留原版本")
            else:
                print(f"  → 保留第一个版本")
        else:
            # 新题目
            seen_questions[normalized_question] = i
            unique_data.append(item)
    
    # 保存结果
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(unique_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n✅ 去重完成!")
        print(f"📊 统计信息:")
        print(f"  原始数据: {len(data)} 条")
        print(f"  去重后: {len(unique_data)} 条")
        print(f"  移除重复: {len(data) - len(unique_data)} 条")
        print(f"  去重率: {((len(data) - len(unique_data)) / len(data) * 100):.1f}%")
        print(f"💾 结果已保存到: {output_file}")
        
    except Exception as e:
        print(f"❌ 保存失败: {str(e)}")

def preview_duplicates(input_file: str, max_show: int = 5):
    """
    预览重复数据，不进行实际去重
    """
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"✅ 成功加载 {len(data)} 条数据")
    except Exception as e:
        print(f"❌ 加载数据失败: {str(e)}")
        return
    
    seen_questions = {}
    duplicate_groups = []
    
    for i, item in enumerate(data):
        doc_token = item.get('doc_token', '')
        normalized_question = normalize_question(doc_token)
        
        if normalized_question in seen_questions:
            original_idx = seen_questions[normalized_question]
            duplicate_groups.append({
                'question': normalized_question,
                'original_idx': original_idx,
                'duplicate_idx': i,
                'original_text': data[original_idx]['doc_token'],
                'duplicate_text': doc_token
            })
        else:
            seen_questions[normalized_question] = i
    
    print(f"\n🔍 发现 {len(duplicate_groups)} 组重复数据")
    print(f"📋 预览前 {min(max_show, len(duplicate_groups))} 组:")
    
    for i, group in enumerate(duplicate_groups[:max_show]):
        print(f"\n--- 重复组 {i+1} ---")
        print(f"标准化题目: {group['question'][:100]}...")
        print(f"原始版本 (索引 {group['original_idx']}, 长度 {len(group['original_text'])}):")
        print(f"  {group['original_text'][:150]}...")
        print(f"重复版本 (索引 {group['duplicate_idx']}, 长度 {len(group['duplicate_text'])}):")
        print(f"  {group['duplicate_text'][:150]}...")

if __name__ == "__main__":
    # 使用示例
    input_file = "your_data.json"  # 替换为你的文件路径
    output_file = "deduplicated_data.json"
    
    print("数据去重工具")
    print("=" * 50)
    
    # 选择操作
    print("请选择操作:")
    print("1. 预览重复数据（不进行去重）")
    print("2. 执行去重（保留最长版本）")
    print("3. 执行去重（保留第一个版本）")
    
    choice = input("请输入选择 (1/2/3): ").strip()
    
    if choice == "1":
        preview_duplicates(input_file)
    elif choice == "2":
        deduplicate_data(input_file, output_file, keep_longest=True)
    elif choice == "3":
        deduplicate_data(input_file, output_file, keep_longest=False)
    else:
        print("无效选择")
        
    print("\n完成！")
