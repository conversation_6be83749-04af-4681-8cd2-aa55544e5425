import os
import json
import requests
import logging
import time
from typing import List, Dict, Optional
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from tqdm import tqdm

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("label_processing.log"),
        logging.StreamHandler()
    ]
)

class DifyLabelProcessor:
    def __init__(self, dify_api_key: str, workflow_id: str,
                 input_json_path: str = r"D:\python_project\dify-图片转解析\wos_train_small_labels_numbered.json",
                 output_json_path: str = "processed_labels_output.json",
                 dify_base_url: str = "https://dify.xmdas-link.com"):
        """
        初始化Dify标签处理器
        """
        self.dify_api_key = dify_api_key
        self.workflow_id = workflow_id
        self.input_json_path = input_json_path
        self.output_json_path = output_json_path
        self.dify_base_url = dify_base_url
        
        # 设置API地址
        self.dify_workflow_url = f"{self.dify_base_url}/v1/workflows/run"
        self.headers = {
            "Authorization": f"Bearer {self.dify_api_key}",
            "Content-Type": "application/json"
        }
        
        # 创建优化的HTTP会话
        self.session = self.create_optimized_session()
        
        # 存储处理结果
        self.processed_results = []
        
        # 加载已存在的结果（支持断点续传）
        self.load_existing_results()

    def create_optimized_session(self) -> requests.Session:
        """创建优化的HTTP会话，支持连接池和重试"""
        session = requests.Session()
        
        # 重试策略
        retry_strategy = Retry(
            total=3,
            backoff_factor=0.5,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        
        # 连接池配置
        adapter = HTTPAdapter(
            pool_connections=20,
            pool_maxsize=20,
            max_retries=retry_strategy
        )
        
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        return session

    def load_existing_results(self) -> None:
        """从输出文件加载已存在的结果，支持断点续传"""
        try:
            if os.path.exists(self.output_json_path):
                with open(self.output_json_path, 'r', encoding='utf-8') as f:
                    self.processed_results = json.load(f)
                    print(f"已加载 {len(self.processed_results)} 个已存在的结果")
        except Exception as e:
            print(f"加载已存在结果时发生错误: {str(e)}")
            self.processed_results = []

    def load_input_data(self) -> List[Dict]:
        """加载输入JSON文件"""
        try:
            with open(self.input_json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                print(f"成功加载 {len(data)} 条输入数据")
                return data
        except Exception as e:
            logging.error(f"加载输入文件失败: {str(e)}")
            return []

    def run_dify_workflow(self, label_path: str, example_questions: List[str]) -> Optional[str]:
        """调用Dify工作流"""
        try:
            # 构建payload，根据你的Dify工作流输入字段配置
            # 将example_questions转换为字符串格式，因为Dify可能需要字符串输入
            example_questions_str = "\n".join([f"{i+1}、{q}" for i, q in enumerate(example_questions)])

            payload = {
                "inputs": {
                    "label_path": label_path,
                    "example_questions": example_questions_str  
                },
                "response_mode": "blocking",
                "user": "label_processor_script",
                "workflow": self.workflow_id
            }

            response = self.session.post(
                self.dify_workflow_url,
                headers=self.headers,
                json=payload,
                timeout=120  # 增加超时时间
            )

            # 检查响应内容是否为空
            if not response.text.strip():
                logging.warning("Dify API返回空响应")
                return None

            response.raise_for_status()

            try:
                result = response.json()
            except json.JSONDecodeError as e:
                logging.error(f"解析JSON响应失败: {str(e)}")
                return None

            # 提取输出结果
            if "data" in result and "outputs" in result["data"]:
                outputs = result["data"]["outputs"]
                if "result" in outputs:
                    return str(outputs["result"])
                elif "output" in outputs:
                    return str(outputs["output"])
                else:
                    return str(outputs)
            elif "output" in result:
                return str(result["output"])
            else:
                logging.warning(f"未找到预期的输出格式: {result}")
                return None

        except requests.exceptions.Timeout:
            logging.error("请求超时")
            return None
        except requests.exceptions.RequestException as e:
            logging.error(f"请求失败: {str(e)}")
            return None
        except Exception as e:
            logging.error(f"调用Dify工作流时发生未知错误: {str(e)}")
            return None

    def parse_dify_output(self, output: str, original_label_path: str) -> Dict:
        """解析Dify输出，提取example_questions
        针对保证的输出格式: "1、question1", "2、question2", "3、question3"
        """
        try:
            example_questions = []

            # 优先尝试解析JSON格式的输出
            if self.try_parse_json_output(output, example_questions):
                pass  # 已经解析成功
            else:
                # 如果不是JSON格式，直接解析为问题列表
                example_questions = self.parse_text_to_questions(output)

            # 构造最终结果，保持与输入文件相同的格式
            result = {
                "label_path": original_label_path,
                "example_questions": example_questions if example_questions else [str(output)]
            }

            return result

        except Exception as e:
            logging.error(f"解析Dify输出失败: {str(e)}")
            # 返回默认结构
            return {
                "label_path": original_label_path,
                "example_questions": [str(output)]
            }

    def try_parse_json_output(self, output: str, example_questions: List[str]) -> bool:
        """尝试解析JSON格式的输出"""
        try:
            # 方法1: 检查是否包含 "example_questions": [ 格式
            if '"example_questions":' in output:
                # 找到数组开始和结束位置
                start_idx = output.find('[')
                end_idx = output.rfind(']')
                if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                    array_content = output[start_idx:end_idx+1]

                    # 尝试多种解析方法
                    parsed_questions = self.parse_json_array(array_content)
                    if parsed_questions:
                        example_questions.extend(parsed_questions)
                        return True

            # 方法2: 检查是否包含JSON代码块
            elif "```json" in output:
                start_marker = "```json"
                end_marker = "```"
                start_idx = output.find(start_marker)
                if start_idx != -1:
                    start_idx += len(start_marker)
                    end_idx = output.find(end_marker, start_idx)
                    if end_idx != -1:
                        json_content = output[start_idx:end_idx].strip()
                        try:
                            parsed_data = json.loads(json_content)
                            questions = parsed_data.get("example_questions", [])
                            if questions:
                                example_questions.extend(questions)
                                return True
                        except json.JSONDecodeError:
                            pass

            # 方法3: 尝试直接解析整个输出为JSON
            else:
                try:
                    parsed_data = json.loads(output)
                    questions = parsed_data.get("example_questions", [])
                    if questions:
                        example_questions.extend(questions)
                        return True
                except json.JSONDecodeError:
                    pass

            return False

        except Exception as e:
            logging.warning(f"JSON解析尝试失败: {str(e)}")
            return False

    def parse_json_array(self, array_content: str) -> List[str]:
        """解析JSON数组内容"""
        try:
            # 方法1: 使用json.loads
            try:
                return json.loads(array_content)
            except json.JSONDecodeError:
                pass

            # 方法2: 使用ast.literal_eval
            import ast
            try:
                return ast.literal_eval(array_content)
            except (ValueError, SyntaxError):
                pass

            # 方法3: 手动解析
            return self.manual_parse_array(array_content)

        except Exception as e:
            logging.warning(f"JSON数组解析失败: {str(e)}")
            return []

    def parse_text_to_questions(self, text: str) -> List[str]:
        """将文本解析为问题列表
        专门针对格式: "1、question1", "2、question2", "3、question3"
        """
        try:
            questions = []

            # 如果文本看起来像是一个数组字符串，先尝试解析
            if text.strip().startswith('[') and text.strip().endswith(']'):
                parsed_questions = self.parse_json_array(text.strip())
                if parsed_questions:
                    return parsed_questions

            # 按行分割文本
            lines = text.strip().split('\n')
            current_question = ""

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # 移除可能的引号和逗号
                line = line.strip('"').strip("'").rstrip(',')

                # 检查是否是新问题的开始（以数字、开头）
                import re
                if re.match(r'^\d+、', line):
                    # 如果有当前问题，先保存
                    if current_question:
                        questions.append(current_question.strip())
                    current_question = line
                else:
                    # 继续当前问题（多行情况）
                    if current_question:
                        current_question += " " + line
                    else:
                        # 如果没有数字开头，可能是纯文本格式
                        current_question = line

            # 添加最后一个问题
            if current_question:
                questions.append(current_question.strip())

            # 如果没有解析出问题，尝试其他方法
            if not questions:
                questions = self.fallback_parse_questions(text)

            return questions if questions else [text]

        except Exception as e:
            logging.error(f"解析文本为问题列表失败: {str(e)}")
            return [text]

    def fallback_parse_questions(self, text: str) -> List[str]:
        """备用解析方法，处理各种可能的格式"""
        try:
            questions = []

            # 尝试按逗号分割（如果是逗号分隔的格式）
            if ',' in text and not '\n' in text:
                parts = text.split(',')
                for part in parts:
                    part = part.strip().strip('"').strip("'")
                    if part:
                        questions.append(part)

            # 尝试按句号分割
            elif '。' in text:
                parts = text.split('。')
                for i, part in enumerate(parts):
                    part = part.strip()
                    if part:
                        # 如果不是以数字、开头，添加序号
                        import re
                        if not re.match(r'^\d+、', part):
                            part = f"{i+1}、{part}"
                        questions.append(part)

            return questions

        except Exception as e:
            logging.warning(f"备用解析方法失败: {str(e)}")
            return []

    def manual_parse_array(self, array_content: str) -> List[str]:
        """手动解析数组内容"""
        try:
            questions = []
            # 移除开头和结尾的方括号
            content = array_content.strip()[1:-1]

            # 按照 ", 分割，但要注意引号内的逗号
            current_item = ""
            in_quotes = False
            escape_next = False

            for char in content:
                if escape_next:
                    current_item += char
                    escape_next = False
                elif char == '\\':
                    current_item += char
                    escape_next = True
                elif char == '"' and not escape_next:
                    in_quotes = not in_quotes
                    current_item += char
                elif char == ',' and not in_quotes:
                    # 找到一个完整的项目
                    item = current_item.strip()
                    if item.startswith('"') and item.endswith('"'):
                        item = item[1:-1]  # 移除引号
                        # 处理转义字符
                        item = item.replace('\\"', '"')
                        item = item.replace('\\\\', '\\')
                        questions.append(item)
                    current_item = ""
                else:
                    current_item += char

            # 处理最后一个项目
            if current_item.strip():
                item = current_item.strip()
                if item.startswith('"') and item.endswith('"'):
                    item = item[1:-1]
                    item = item.replace('\\"', '"')
                    item = item.replace('\\\\', '\\')
                    questions.append(item)

            return questions if questions else [array_content]

        except Exception as e:
            logging.error(f"手动解析数组失败: {str(e)}")
            return [array_content]

    def process_single_item(self, item: Dict, index: int) -> Optional[Dict]:
        """处理单个数据项"""
        try:
            label_path = item.get("label_path", "")
            example_questions = item.get("example_questions", [])
            
            if not label_path:
                logging.warning(f"第 {index + 1} 项缺少label_path")
                return None
            
            print(f"正在处理第 {index + 1} 项: {label_path[:50]}...")
            
            # 调用Dify工作流
            dify_output = self.run_dify_workflow(label_path, example_questions)
            
            if dify_output is None:
                logging.error(f"第 {index + 1} 项处理失败")
                return None
            
            # 解析输出
            result = self.parse_dify_output(dify_output, label_path)
            
            return result
            
        except Exception as e:
            logging.error(f"处理第 {index + 1} 项时发生错误: {str(e)}")
            return None

    def save_results(self) -> None:
        """保存处理结果到JSON文件"""
        try:
            with open(self.output_json_path, 'w', encoding='utf-8') as f:
                json.dump(self.processed_results, f, ensure_ascii=False, indent=2)
            
            file_size = os.path.getsize(self.output_json_path) / 1024  # KB
            print(f"✅ 结果已保存到: {self.output_json_path} ({len(self.processed_results)}个结果, {file_size:.1f}KB)")
            
        except Exception as e:
            logging.error(f"保存结果失败: {str(e)}")

    def process_all_items(self) -> None:
        """处理所有数据项"""
        # 加载输入数据
        input_data = self.load_input_data()
        if not input_data:
            print("没有找到可处理的数据")
            return
        
        # 获取已处理的label_path，用于断点续传
        processed_label_paths = {item.get("label_path", "") for item in self.processed_results}
        
        # 过滤出未处理的数据
        remaining_data = [item for item in input_data 
                         if item.get("label_path", "") not in processed_label_paths]
        
        if not remaining_data:
            print("所有数据都已处理完成！")
            return
        
        print(f"开始处理 {len(remaining_data)} 个未处理的数据项...")
        
        success_count = 0
        failed_count = 0
        
        # 使用tqdm显示进度
        for index, item in enumerate(tqdm(remaining_data, desc="处理进度")):
            result = self.process_single_item(item, index)
            
            if result:
                self.processed_results.append(result)
                success_count += 1
                
                # 每处理10个项目保存一次结果
                if success_count % 10 == 0:
                    self.save_results()
            else:
                failed_count += 1
            
            # 每个请求之间稍作延迟，避免API限流
            time.sleep(0.5)
        
        # 最终保存结果
        self.save_results()
        
        print(f"\n📈 处理完成统计:")
        print(f"   总数量: {len(remaining_data)}")
        print(f"   成功: {success_count}")
        print(f"   失败: {failed_count}")
        print(f"   总结果数: {len(self.processed_results)}")

    def test_single_item(self, index: int = 0) -> None:
        """测试处理单个数据项"""
        input_data = self.load_input_data()
        if not input_data or index >= len(input_data):
            print(f"无法找到索引为 {index} 的数据项")
            return

        item = input_data[index]
        print(f"测试处理第 {index + 1} 项:")
        print(f"Label Path: {item.get('label_path', '')}")
        print(f"Example Questions数量: {len(item.get('example_questions', []))}")
        print("\n开始调用Dify API...")

        result = self.process_single_item(item, index)

        if result:
            print("\n✅ 处理成功！")
            print(f"输出的Label Path: {result['label_path']}")
            print(f"输出的Example Questions数量: {len(result['example_questions'])}")
            print("\n输出的Example Questions:")
            for i, q in enumerate(result['example_questions'][:3]):  # 只显示前3个
                print(f"  {i+1}. {q[:100]}...")

            # 保存测试结果
            test_output_path = "test_result.json"
            with open(test_output_path, 'w', encoding='utf-8') as f:
                json.dump([result], f, ensure_ascii=False, indent=2)
            print(f"\n测试结果已保存到: {test_output_path}")
        else:
            print("\n❌ 处理失败！")


if __name__ == "__main__":
    import sys

    # 从get_analysis.py中获取的配置信息
    DIFY_API_KEY = "app-vFRSxQl0lfmPukuyeyQjWeZg"
    WORKFLOW_ID = "4KdwtghEk8bN2yUz"  # 请确认这是正确的workflow_id
    DIFY_BASE_URL = "https://dify.xmdas-link.com"

    processor = DifyLabelProcessor(
        dify_api_key=DIFY_API_KEY,
        workflow_id=WORKFLOW_ID,
        input_json_path=r"D:\python_project\dify-图片转解析\ultimate_merge_small_labels_numbered.json",
        output_json_path="processed_labels_output.json",
        dify_base_url=DIFY_BASE_URL
    )

    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == "--test":
        # 测试模式
        test_index = 0
        if len(sys.argv) > 2:
            try:
                test_index = int(sys.argv[2])
            except ValueError:
                print("测试索引必须是数字")
                sys.exit(1)

        print(f"🧪 启动测试模式，测试第 {test_index + 1} 项数据")
        processor.test_single_item(test_index)
    else:
        # 完整处理模式
        print("🚀 启动完整处理模式")
        print("💡 提示: 使用 --test [索引] 参数可以测试单个数据项")
        print("   例如: python process_labels_with_dify.py --test 0")
        processor.process_all_items()
