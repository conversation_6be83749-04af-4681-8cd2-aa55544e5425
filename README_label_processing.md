# Dify标签处理脚本使用说明

## 功能描述

这个脚本用于读取 `wos_train_small_labels_numbered.json` 文件中的数据，逐个发送到Dify API进行处理，并将结果保存为相同格式的JSON文件。

## 输入格式

输入文件应包含以下格式的数据：
```json
[
  {
    "label_path": "小学数学新知识树 -> 数与代数 -> 数的运算 -> 分数的四则运算 -> 分数乘法 -> 分数与整数的乘法 -> 整数乘分数",
    "example_questions": [
      "1、题目内容...",
      "2、题目内容...",
      "3、题目内容..."
    ]
  }
]
```

## 输出格式

输出文件将保持相同的格式，但 `example_questions` 将是Dify处理后的结果：
```json
[
  {
    "label_path": "小学数学新知识树 -> 数与代数 -> 数的运算 -> 分数的四则运算 -> 分数乘法 -> 分数与整数的乘法 -> 整数乘分数",
    "example_questions": [
      "1、Dify生成的题目...",
      "2、Dify生成的题目...",
      "3、Dify生成的题目..."
    ]
  }
]
```

## 使用方法

### 1. 测试模式（推荐先使用）

测试第一个数据项：
```bash
python process_labels_with_dify.py --test
```

测试指定索引的数据项（例如第3个，索引为2）：
```bash
python process_labels_with_dify.py --test 2
```

### 2. 完整处理模式

处理所有数据：
```bash
python process_labels_with_dify.py
```

## 配置说明

在脚本中需要配置以下参数：

- `DIFY_API_KEY`: 你的Dify API密钥
- `WORKFLOW_ID`: 你的Dify工作流ID
- `DIFY_BASE_URL`: 你的Dify部署地址

当前配置（来自get_analysis.py）：
```python
DIFY_API_KEY = "app-m34jaS5NMuIT5yGViXWyq0g1"
WORKFLOW_ID = "vKDOeCan7OaTb3u0"
DIFY_BASE_URL = "https://dify.xmdas-link.com"
```

## Dify工作流输入字段

脚本会向Dify发送以下输入：
- `label_path`: 标签路径字符串
- `example_questions`: 示例问题字符串（多个问题用换行符分隔）

请确保你的Dify工作流配置了这两个输入字段。

## 断点续传

脚本支持断点续传功能：
- 已处理的结果会保存在 `processed_labels_output.json` 文件中
- 重新运行脚本时，会自动跳过已处理的数据项
- 每处理10个项目会自动保存一次结果

## 日志记录

- 处理日志会保存在 `label_processing.log` 文件中
- 控制台也会显示处理进度和状态

## 注意事项

1. **API限流**: 脚本在每个请求之间会有0.5秒的延迟，避免触发API限流
2. **超时设置**: 每个请求的超时时间设置为120秒
3. **错误处理**: 脚本包含完善的错误处理和重试机制
4. **输出解析**: 脚本会自动解析Dify的JSON输出，也支持文本格式的输出

## 故障排除

### 1. API调用失败
- 检查API密钥是否正确
- 检查工作流ID是否正确
- 检查网络连接

### 2. 输出格式不正确
- 检查Dify工作流的输出格式设置
- 查看日志文件了解具体错误信息

### 3. 处理中断
- 脚本支持断点续传，重新运行即可继续处理
- 检查 `processed_labels_output.json` 文件确认已处理的数据

## 文件说明

- `process_labels_with_dify.py`: 主处理脚本
- `wos_train_small_labels_numbered.json`: 输入数据文件
- `processed_labels_output.json`: 输出结果文件
- `test_result.json`: 测试模式的输出文件
- `label_processing.log`: 处理日志文件
