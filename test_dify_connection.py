#!/usr/bin/env python3
"""
测试Dify API连接的简单脚本
"""

import requests
import json

def test_dify_connection():
    """测试Dify API连接"""
    
    # 配置信息（与主脚本保持一致）
    DIFY_API_KEY = "app-vFRSxQl0lfmPukuyeyQjWeZg"
    WORKFLOW_ID = "4KdwtghEk8bN2yUz"
    DIFY_BASE_URL = "https://dify.xmdas-link.com"
    
    # API地址
    dify_workflow_url = f"{DIFY_BASE_URL}/v1/workflows/run"
    
    # 请求头
    headers = {
        "Authorization": f"Bearer {DIFY_API_KEY}",
        "Content-Type": "application/json"
    }
    
    # 测试数据
    test_label_path = "小学数学新知识树 -> 数与代数 -> 数的运算 -> 分数的四则运算"
    test_example_questions = "1、测试题目：计算 1/2 + 1/3 = ?\n2、测试题目：计算 2/3 × 3/4 = ?"
    
    # 构建请求payload
    payload = {
        "inputs": {
            "label_path": test_label_path,
            "exmple_questions": test_example_questions  # 注意：这里是 exmple_questions，不是 example_questions
        },
        "response_mode": "blocking",
        "user": "test_script",
        "workflow": WORKFLOW_ID
    }
    
    print("🔗 测试Dify API连接...")
    print(f"API地址: {dify_workflow_url}")
    print(f"工作流ID: {WORKFLOW_ID}")
    print(f"测试数据:")
    print(f"  Label Path: {test_label_path}")
    print(f"  Example Questions: {test_example_questions}")
    print("\n发送请求...")
    
    try:
        response = requests.post(
            dify_workflow_url,
            headers=headers,
            json=payload,
            timeout=60
        )
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ API连接成功！")
            
            try:
                result = response.json()
                print("\n📄 响应内容:")
                print(json.dumps(result, indent=2, ensure_ascii=False))
                
                # 尝试提取输出
                if "data" in result and "outputs" in result["data"]:
                    outputs = result["data"]["outputs"]
                    if "output" in outputs:
                        print(f"\n🎯 提取的输出: {outputs['output']}")
                    else:
                        print(f"\n🎯 提取的输出: {outputs}")
                elif "output" in result:
                    print(f"\n🎯 提取的输出: {result['output']}")
                else:
                    print("\n⚠️ 未找到预期的输出格式")
                    
            except json.JSONDecodeError:
                print("⚠️ 响应不是有效的JSON格式")
                print(f"原始响应: {response.text}")
                
        else:
            print(f"❌ API调用失败，状态码: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
    except requests.exceptions.ConnectionError:
        print("❌ 连接错误，请检查网络和API地址")
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {str(e)}")
    except Exception as e:
        print(f"❌ 未知错误: {str(e)}")

def test_api_endpoints():
    """测试不同的API端点"""
    
    DIFY_API_KEY = "app-vFRSxQl0lfmPukuyeyQjWeZg"
    DIFY_BASE_URL = "https://dify.xmdas-link.com"
    
    headers = {
        "Authorization": f"Bearer {DIFY_API_KEY}",
        "Content-Type": "application/json"
    }
    
    # 测试不同的端点
    endpoints = [
        f"{DIFY_BASE_URL}/v1/workflows/run",
        f"{DIFY_BASE_URL}/api/v1/workflows/run",
        f"{DIFY_BASE_URL}/v1/parameters",
        f"{DIFY_BASE_URL}/api/v1/parameters"
    ]
    
    print("🔍 测试不同的API端点...")
    
    for endpoint in endpoints:
        print(f"\n测试端点: {endpoint}")
        try:
            response = requests.get(endpoint, headers=headers, timeout=10)
            print(f"  状态码: {response.status_code}")
            if response.status_code != 404:
                print(f"  响应长度: {len(response.text)} 字符")
        except Exception as e:
            print(f"  错误: {str(e)}")

if __name__ == "__main__":
    print("=" * 60)
    print("Dify API连接测试")
    print("=" * 60)
    
    # 首先测试基本连接
    test_dify_connection()
    
    print("\n" + "=" * 60)
    
    # 测试不同端点
    test_api_endpoints()
    
    print("\n" + "=" * 60)
    print("测试完成")
