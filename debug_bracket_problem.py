#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试括号问题的相似度计算
"""

from label_grouped_deduplication import LabelGroupedDeduplicator

def debug_bracket_problem():
    """调试括号问题"""
    print("🔍 调试括号问题的相似度计算")
    
    deduplicator = LabelGroupedDeduplicator("", "", 0.999)
    
    text1 = "按一定比放大后长是36厘米宽是18厘米它是按( )( )放大 设原长为$L$原宽为$W$放大后的长为$L'$放大后的宽为$W'$根据题目有$L' = 36$厘米$W' = 18$厘米\n设放大比例为$k$则有$L' = k \\times L$$W' = k \\times W$\n因此$k = \\frac{L'}{L} = \\frac{W'}{W}$\n假设原长$L = 18$厘米原宽$W = 9$厘米（这是一个合理的假设因为这样放大比例为2）\n则$k = \\frac{36}{18} = \\frac{18}{9} = 2$\n因此放大比例为$21$"
    
    text2 = "按一定比放大后长是36厘米宽是18厘米它是按（ ） （ ）放大 设原长为$l_0$原宽为$w_0$放大后的长为$l=36$厘米宽为$w=18$厘米放大比例为$k$则有$l = k \\times l_0$和$w = k \\times w_0$因此$k = \\frac{l}{l_0} = \\frac{w}{w_0}$假设原长$l_0 = 18$厘米原宽$w_0 = 9$厘米（这是常见的原始尺寸）则$k = \\frac{36}{18} = \\frac{18}{9} = 2$即放大比例为$21$"
    
    print("文本1长度:", len(text1))
    print("文本2长度:", len(text2))
    print()
    
    # 提取题目部分
    question1 = deduplicator.extract_question_part(text1)
    question2 = deduplicator.extract_question_part(text2)
    
    print("题目1:", repr(question1))
    print("题目2:", repr(question2))
    print()
    print("题目1长度:", len(question1))
    print("题目2长度:", len(question2))
    print()
    
    # 标准化处理
    norm1 = deduplicator.normalize_text(question1)
    norm2 = deduplicator.normalize_text(question2)
    
    print("标准化题目1:", repr(norm1))
    print("标准化题目2:", repr(norm2))
    print()
    print("标准化题目1长度:", len(norm1))
    print("标准化题目2长度:", len(norm2))
    print()
    
    # 计算相似度（基于题目部分）
    similarity = deduplicator.calculate_similarity(question1, question2)
    print(f"相似度: {similarity:.6f}")
    print(f"阈值: {deduplicator.threshold}")
    print(f"是否会被认为重复: {'是' if similarity >= deduplicator.threshold else '否'}")
    
    # 检查是否完全相同
    if norm1 == norm2:
        print("✅ 标准化后完全相同")
    else:
        print("❌ 标准化后不完全相同")
        
        # 找出差异
        print("\n差异分析:")
        if len(norm1) != len(norm2):
            print(f"长度不同: {len(norm1)} vs {len(norm2)}")
        
        min_len = min(len(norm1), len(norm2))
        for i in range(min_len):
            if norm1[i] != norm2[i]:
                print(f"第{i}个字符不同: '{norm1[i]}' (ord:{ord(norm1[i])}) vs '{norm2[i]}' (ord:{ord(norm2[i])})")
                print(f"前后文1: ...{norm1[max(0,i-10):i+10]}...")
                print(f"前后文2: ...{norm2[max(0,i-10):i+10]}...")
                break
        
        # 显示字符差异
        print("\n逐字符对比:")
        for i in range(min(len(norm1), len(norm2), 50)):  # 只显示前50个字符
            c1, c2 = norm1[i], norm2[i]
            status = "✓" if c1 == c2 else "✗"
            print(f"{i:2d}: '{c1}' vs '{c2}' {status}")

if __name__ == "__main__":
    debug_bracket_problem()
