#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试相似度计算问题
"""

from label_grouped_deduplication import LabelGroupedDeduplicator

def debug_salt_problem():
    """调试盐袋问题的相似度计算"""
    print("🔍 调试盐袋问题的相似度计算")
    
    deduplicator = LabelGroupedDeduplicator("", "", 0.999)
    
    text1 = "有5袋盐,其中4袋每袋500g,另一袋不是500g,但不知道是比500g重还是轻如果用天平称,那么至少称几次可以保证找出这袋盐? 首先将5袋盐分为三组两组各有2袋一组有1袋第一次称重取两组各有2袋的盐进行称重若天平平衡则不同的那袋盐在单独的1袋中再称一次即可确定这袋盐是重还是轻若天平不平衡则不同的那袋盐在这4袋中接下来从这两组中各取1袋进行第二次称重若第二次称重天平平衡则不同的那袋盐在未取的2袋中若不平衡则在已取的2袋中再进行一次称重即可确定具体是哪一袋以及它是重还是轻因此至少需要称2次可以保证找出这袋盐"
    
    text2 = "有5袋盐,其中4袋每袋500g,另一袋不是500g,但不知道是比500g重还是轻如果用天平称,那么至少称几次可以保证找出这袋盐? 首先将5袋盐分为三组两组各2袋一组1袋\n1. 第一次称重取两组各2袋进行称重\n- 如果天平平衡说明这4袋盐都是500g不同的那袋在剩下的1袋中再称一次即可确定\n- 如果天平不平衡不同的那袋在这4袋之中\n2. 第二次称重从不平衡的两组中各取1袋进行称重\n- 如果天平平衡说明取出的这两袋是500g不同的那袋在未取的两袋中\n- 如果天平不平衡可以确定不同的那袋是较重或较轻的那一袋\n因此至少称2次可以保证找出这袋盐"
    
    print("文本1长度:", len(text1))
    print("文本2长度:", len(text2))
    print()
    
    # 提取题目部分
    question1 = deduplicator.extract_question_part(text1)
    question2 = deduplicator.extract_question_part(text2)
    
    print("题目1:", question1[:100] + "...")
    print("题目2:", question2[:100] + "...")
    print()
    
    # 标准化处理
    norm1 = deduplicator.normalize_text(question1)
    norm2 = deduplicator.normalize_text(question2)
    
    print("标准化题目1长度:", len(norm1))
    print("标准化题目2长度:", len(norm2))
    print()
    print("标准化题目1:", norm1[:200] + "...")
    print("标准化题目2:", norm2[:200] + "...")
    print()
    
    # 计算相似度
    similarity = deduplicator.calculate_similarity(text1, text2)
    print(f"相似度: {similarity:.6f}")
    print(f"阈值: {deduplicator.threshold}")
    print(f"是否会被认为重复: {'是' if similarity >= deduplicator.threshold else '否'}")
    
    # 检查是否完全相同
    if norm1 == norm2:
        print("✅ 标准化后完全相同")
    else:
        print("❌ 标准化后不完全相同")
        
        # 找出差异
        print("\n差异分析:")
        min_len = min(len(norm1), len(norm2))
        for i in range(min_len):
            if norm1[i] != norm2[i]:
                print(f"第{i}个字符不同: '{norm1[i]}' vs '{norm2[i]}'")
                print(f"前后文: ...{norm1[max(0,i-10):i+10]}...")
                print(f"前后文: ...{norm2[max(0,i-10):i+10]}...")
                break
        
        if len(norm1) != len(norm2):
            print(f"长度不同: {len(norm1)} vs {len(norm2)}")

if __name__ == "__main__":
    debug_salt_problem()
