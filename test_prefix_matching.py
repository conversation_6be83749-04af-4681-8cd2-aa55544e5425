#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试前缀匹配去重方法
"""

from label_grouped_deduplication import LabelGroupedDeduplicator

def test_prefix_matching():
    """测试前缀匹配方法"""
    print("🔍 测试前缀匹配去重方法")
    
    # 测试用例
    test_cases = [
        {
            "name": "括号问题",
            "text1": "按一定比放大后长是36厘米宽是18厘米它是按( )( )放大 设原长为$L$原宽为$W$...",
            "text2": "按一定比放大后长是36厘米宽是18厘米它是按（ ） （ ）放大 设原长为$l_0$原宽为$w_0$...",
            "expected": True
        },
        {
            "name": "盐袋问题",
            "text1": "有5袋盐,其中4袋每袋500g,另一袋不是500g,但不知道是比500g重还是轻如果用天平称,那么至少称几次可以保证找出这袋盐? 首先将5袋盐分为三组...",
            "text2": "有5袋盐,其中4袋每袋500g,另一袋不是500g,但不知道是比500g重还是轻如果用天平称,那么至少称几次可以保证找出这袋盐? 首先将5袋盐分为三组两组各2袋一组1袋...",
            "expected": True
        },
        {
            "name": "完全不同的题目",
            "text1": "小明有5个苹果，吃了2个，还剩几个？",
            "text2": "计算：3+4=?",
            "expected": False
        },
        {
            "name": "相似但不同的题目",
            "text1": "小明有5个苹果，吃了2个，还剩几个？",
            "text2": "小红有8支铅笔，用了3支，还有几支？",
            "expected": False
        }
    ]
    
    # 测试不同的前缀长度
    prefix_lengths = [15, 20, 25, 30]
    
    for prefix_len in prefix_lengths:
        print(f"\n{'='*60}")
        print(f"测试前缀长度: {prefix_len}")
        print(f"{'='*60}")
        
        deduplicator = LabelGroupedDeduplicator("", "", 0.9, prefix_len)
        
        for case in test_cases:
            print(f"\n测试用例: {case['name']}")
            
            prefix1 = deduplicator.extract_question_prefix(case['text1'])
            prefix2 = deduplicator.extract_question_prefix(case['text2'])
            
            is_match = (prefix1 == prefix2)
            
            print(f"前缀1: '{prefix1}'")
            print(f"前缀2: '{prefix2}'")
            print(f"匹配结果: {is_match}")
            print(f"预期结果: {case['expected']}")
            
            if is_match == case['expected']:
                print("✅ 测试通过")
            else:
                print("❌ 测试失败")

def test_real_examples():
    """测试真实的例子"""
    print("\n" + "="*80)
    print("测试真实例子")
    print("="*80)
    
    # 真实的重复例子
    real_cases = [
        {
            "name": "放大问题",
            "text1": "按一定比放大后长是36厘米宽是18厘米它是按( )( )放大 设原长为$L$原宽为$W$放大后的长为$L'$放大后的宽为$W'$根据题目有$L' = 36$厘米$W' = 18$厘米\n设放大比例为$k$则有$L' = k \\times L$$W' = k \\times W$\n因此$k = \\frac{L'}{L} = \\frac{W'}{W}$\n假设原长$L = 18$厘米原宽$W = 9$厘米（这是一个合理的假设因为这样放大比例为2）\n则$k = \\frac{36}{18} = \\frac{18}{9} = 2$\n因此放大比例为$21$",
            "text2": "按一定比放大后长是36厘米宽是18厘米它是按（ ） （ ）放大 设原长为$l_0$原宽为$w_0$放大后的长为$l=36$厘米宽为$w=18$厘米放大比例为$k$则有$l = k \\times l_0$和$w = k \\times w_0$因此$k = \\frac{l}{l_0} = \\frac{w}{w_0}$假设原长$l_0 = 18$厘米原宽$w_0 = 9$厘米（这是常见的原始尺寸）则$k = \\frac{36}{18} = \\frac{18}{9} = 2$即放大比例为$21$"
        },
        {
            "name": "盐袋问题",
            "text1": "有5袋盐,其中4袋每袋500g,另一袋不是500g,但不知道是比500g重还是轻如果用天平称,那么至少称几次可以保证找出这袋盐? 首先将5袋盐分为三组两组各有2袋一组有1袋第一次称重取两组各有2袋的盐进行称重若天平平衡则不同的那袋盐在单独的1袋中再称一次即可确定这袋盐是重还是轻若天平不平衡则不同的那袋盐在这4袋中接下来从这两组中各取1袋进行第二次称重若第二次称重天平平衡则不同的那袋盐在未取的2袋中若不平衡则在已取的2袋中再进行一次称重即可确定具体是哪一袋以及它是重还是轻因此至少需要称2次可以保证找出这袋盐",
            "text2": "有5袋盐,其中4袋每袋500g,另一袋不是500g,但不知道是比500g重还是轻如果用天平称,那么至少称几次可以保证找出这袋盐? 首先将5袋盐分为三组两组各2袋一组1袋\n1. 第一次称重取两组各2袋进行称重\n- 如果天平平衡说明这4袋盐都是500g不同的那袋在剩下的1袋中再称一次即可确定\n- 如果天平不平衡不同的那袋在这4袋之中\n2. 第二次称重从不平衡的两组中各取1袋进行称重\n- 如果天平平衡说明取出的这两袋是500g不同的那袋在未取的两袋中\n- 如果天平不平衡可以确定不同的那袋是较重或较轻的那一袋\n因此至少称2次可以保证找出这袋盐"
        }
    ]
    
    deduplicator = LabelGroupedDeduplicator("", "", 0.9, 20)  # 使用20字符前缀
    
    for case in real_cases:
        print(f"\n测试用例: {case['name']}")
        
        prefix1 = deduplicator.extract_question_prefix(case['text1'])
        prefix2 = deduplicator.extract_question_prefix(case['text2'])
        
        is_match = (prefix1 == prefix2)
        
        print(f"文本1长度: {len(case['text1'])}")
        print(f"文本2长度: {len(case['text2'])}")
        print(f"前缀1 (20字符): '{prefix1}'")
        print(f"前缀2 (20字符): '{prefix2}'")
        print(f"前缀匹配: {is_match}")
        
        if is_match:
            print("✅ 正确识别为重复")
        else:
            print("❌ 未能识别为重复")

if __name__ == "__main__":
    test_prefix_matching()
    test_real_examples()
