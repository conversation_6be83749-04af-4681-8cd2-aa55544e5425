#!/usr/bin/env python3
"""
为example_questions中的每个题目添加序号
格式：1、题目内容
     2、题目内容
     ...
"""
import json
import argparse

def add_question_numbers(input_file: str, output_file: str):
    """为题目添加序号"""
    
    print(f"🚀 开始为题目添加序号")
    print(f"📁 输入文件: {input_file}")
    print(f"📁 输出文件: {output_file}")
    print("=" * 60)
    
    # 读取原始数据
    print(f"📖 读取数据文件: {input_file}")
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"📊 原始数据: {len(data)} 个条目")
    
    # 为每个条目的题目添加序号
    modified_data = []
    total_questions = 0
    
    for item in data:
        label_path = item["label_path"]
        questions = item["example_questions"]
        
        # 为题目添加序号
        numbered_questions = []
        for i, question in enumerate(questions, 1):
            numbered_question = f"{i}、{question}"
            numbered_questions.append(numbered_question)
        
        modified_item = {
            "label_path": label_path,
            "example_questions": numbered_questions
        }
        modified_data.append(modified_item)
        total_questions += len(numbered_questions)
    
    print(f"🔄 序号添加完成")
    print(f"📈 处理结果:")
    print(f"  处理条目数: {len(modified_data)}")
    print(f"  处理题目数: {total_questions}")
    
    # 显示前2个条目的示例
    print(f"\n🔍 添加序号后的格式示例:")
    for i, item in enumerate(modified_data[:2], 1):
        print(f"\n{i}. 标签: {item['label_path'][:80]}...")
        print(f"   题目数量: {len(item['example_questions'])}")
        print(f"   题目示例:")
        for j, question in enumerate(item["example_questions"][:3], 1):
            print(f"     {question[:100]}...")
        if len(item["example_questions"]) > 3:
            print(f"     ... (还有{len(item['example_questions'])-3}个题目)")
    
    # 保存修改后的数据
    print(f"\n💾 保存添加序号后的数据到: {output_file}")
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(modified_data, f, ensure_ascii=False, indent=2)
    
    # 保存统计信息
    stats = {
        "total_labels": len(modified_data),
        "total_questions": total_questions,
        "avg_questions_per_label": total_questions / len(modified_data),
        "modification": "Added sequential numbers (1、2、3、...) to each question",
        "format": {
            "structure": "list of objects",
            "fields": ["label_path", "example_questions"],
            "question_format": "序号、题目内容"
        }
    }
    
    stats_file = output_file.replace('.json', '_stats.json')
    with open(stats_file, 'w', encoding='utf-8') as f:
        json.dump(stats, f, ensure_ascii=False, indent=2)
    
    print(f"📊 统计信息保存到: {stats_file}")
    
    print(f"\n✅ 序号添加完成！")
    print(f"📈 修改特点:")
    print(f"  - 每个题目前添加序号：1、2、3、...")
    print(f"  - 保持原有数据结构不变")
    print(f"  - 序号从1开始，每个标签独立编号")
    print(f"  - 使用中文顿号分隔序号和内容")
    
    return modified_data, stats

def main():
#     parser = argparse.ArgumentParser(description="为题目添加序号")
#     parser.add_argument('--input', '-i', type=str, required=True, help='输入JSON文件路径')
#     parser.add_argument('--output', '-o', type=str, required=True, help='输出JSON文件路径')
    
#     args = parser.parse_args()
    
    # 执行添加序号
    input_file = r"D:\python_project\dify-图片转解析\ultimate_merge_small_labels_numbered.json"
    output_file = r"D:\python_project\dify-图片转解析\ultimate_merge_small_labels_numbered.json"
    add_question_numbers(input_file, output_file)

if __name__ == "__main__":
    main()
