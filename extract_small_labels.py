#!/usr/bin/env python3
"""
提取小于40个样本的标签及其对应的所有题目内容
输出格式：{标签: [题目1, 题目2, ...]}
"""
import json
import argparse
from collections import defaultdict

def extract_small_labels(input_file: str, output_file: str, threshold: int = 40):
    """提取小于指定阈值的标签及其所有题目内容"""
    
    print(f"🚀 开始提取小于{threshold}个样本的标签")
    print(f"📁 输入文件: {input_file}")
    print(f"📁 输出文件: {output_file}")
    print("=" * 60)
    
    # 读取原始数据
    print(f"📖 读取数据文件: {input_file}")
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"📊 原始数据集大小: {len(data)} 个样本")
    
    # 按标签分组
    label_groups = defaultdict(list)
    for item in data:
        # 将doc_label列表转换为字符串作为键
        label_key = ' -> '.join(item['doc_label'])
        # 提取doc_token作为题目内容
        doc_token = item.get('doc_token', '')
        label_groups[label_key].append(doc_token)
    
    print(f"📈 发现 {len(label_groups)} 个不同的doc_label")
    
    # 筛选小于阈值的标签
    small_labels = {}
    large_labels_count = 0
    
    for label, tokens in label_groups.items():
        if len(tokens) < threshold:
            small_labels[label] = tokens
        else:
            large_labels_count += 1
    
    print(f"🎯 小于{threshold}个样本的标签: {len(small_labels)} 个")
    print(f"🎯 大于等于{threshold}个样本的标签: {large_labels_count} 个")
    
    # 统计信息
    total_small_samples = sum(len(tokens) for tokens in small_labels.values())
    print(f"📊 小标签总样本数: {total_small_samples}")
    
    # 显示样本数分布
    sample_counts = [len(tokens) for tokens in small_labels.values()]
    if sample_counts:
        print(f"📋 小标签样本数分布:")
        print(f"  最少样本数: {min(sample_counts)}")
        print(f"  最多样本数: {max(sample_counts)}")
        print(f"  平均样本数: {sum(sample_counts) / len(sample_counts):.1f}")
    
    # 显示前10个小标签示例
    print(f"\n🔍 前10个小标签示例:")
    for i, (label, tokens) in enumerate(list(small_labels.items())[:10], 1):
        print(f"  {i:2d}. [{len(tokens):2d}个] {label[:80]}...")
        # 显示第一个题目示例
        if tokens:
            print(f"      示例题目: {tokens[0][:60]}...")
    
    # 保存结果
    print(f"\n💾 保存小标签数据到: {output_file}")
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(small_labels, f, ensure_ascii=False, indent=2)
    
    # 保存统计信息
    stats = {
        "total_labels": len(label_groups),
        "small_labels_count": len(small_labels),
        "large_labels_count": large_labels_count,
        "threshold": threshold,
        "total_small_samples": total_small_samples,
        "small_labels_distribution": {
            "min_samples": min(sample_counts) if sample_counts else 0,
            "max_samples": max(sample_counts) if sample_counts else 0,
            "avg_samples": sum(sample_counts) / len(sample_counts) if sample_counts else 0
        }
    }
    
    stats_file = output_file.replace('.json', '_stats.json')
    with open(stats_file, 'w', encoding='utf-8') as f:
        json.dump(stats, f, ensure_ascii=False, indent=2)
    
    print(f"📊 统计信息保存到: {stats_file}")
    
    print(f"\n✅ 提取完成！")
    print(f"📈 结果摘要:")
    print(f"  小标签数量: {len(small_labels)}")
    print(f"  小标签总样本数: {total_small_samples}")
    print(f"  输出格式: {{标签: [题目1, 题目2, ...]}}")
    
    return small_labels, stats

def main():
    # parser = argparse.ArgumentParser(description="提取小于指定阈值的标签及其题目内容")
    # parser.add_argument('--input', '-i', type=str, required=True, default=r"D:\python_project\dify-图片转解析\merge_deduplicated.json", help='输入JSON文件路径')
    # parser.add_argument('--output', '-o', type=str, required=True, default=r"D:\python_project\dify-图片转解析\merge_small_labels_numbered.json", help='输出JSON文件路径')
    # parser.add_argument('--threshold', '-t', type=int, default=40, help='样本数阈值（默认40）')
    
    # args = parser.parse_args()

    input_file = r"D:\python_project\dify-图片转解析\ultimate_merge.json"
    output_file = r"D:\python_project\dify-图片转解析\ultimate_merge_small_labels_numbered.json"
    
    # 执行提取
    extract_small_labels(input_file, output_file, 40)

if __name__ == "__main__":
    main()
