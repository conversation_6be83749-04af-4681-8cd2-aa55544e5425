#!/usr/bin/env python3
"""
重构小标签数据格式
将 {标签: [题目列表]} 格式转换为 [{label_path: 标签, example_questions: [题目列表]}] 格式
"""
import json
import argparse

def restructure_small_labels(input_file: str, output_file: str):
    """重构小标签数据格式"""
    
    print(f"🚀 开始重构小标签数据格式")
    print(f"📁 输入文件: {input_file}")
    print(f"📁 输出文件: {output_file}")
    print("=" * 60)
    
    # 读取原始数据
    print(f"📖 读取数据文件: {input_file}")
    with open(input_file, 'r', encoding='utf-8') as f:
        original_data = json.load(f)
    
    print(f"📊 原始数据: {len(original_data)} 个标签")
    
    # 转换数据格式
    restructured_data = []
    
    for label_path, questions in original_data.items():
        restructured_item = {
            "label_path": label_path,
            "example_questions": questions
        }
        restructured_data.append(restructured_item)
    
    print(f"🔄 格式转换完成")
    print(f"📈 转换后数据: {len(restructured_data)} 个条目")
    
    # 统计信息
    total_questions = sum(len(item["example_questions"]) for item in restructured_data)
    question_counts = [len(item["example_questions"]) for item in restructured_data]
    
    print(f"📊 数据统计:")
    print(f"  总标签数: {len(restructured_data)}")
    print(f"  总题目数: {total_questions}")
    print(f"  平均每标签题目数: {total_questions / len(restructured_data):.1f}")
    print(f"  最少题目数: {min(question_counts)}")
    print(f"  最多题目数: {max(question_counts)}")
    
    # 显示前3个条目示例
    print(f"\n🔍 前3个条目格式示例:")
    for i, item in enumerate(restructured_data[:3], 1):
        print(f"\n{i}. 条目格式:")
        print(f'   "label_path": "{item["label_path"][:80]}..."')
        print(f'   "example_questions": [')
        for j, question in enumerate(item["example_questions"][:2], 1):
            print(f'     "{question[:60]}...",')
        if len(item["example_questions"]) > 2:
            print(f'     ... (还有{len(item["example_questions"])-2}个题目)')
        print(f'   ]')
        print(f'   题目数量: {len(item["example_questions"])}')
    
    # 保存重构后的数据
    print(f"\n💾 保存重构后的数据到: {output_file}")
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(restructured_data, f, ensure_ascii=False, indent=2)
    
    # 保存统计信息
    stats = {
        "total_labels": len(restructured_data),
        "total_questions": total_questions,
        "avg_questions_per_label": total_questions / len(restructured_data),
        "min_questions": min(question_counts),
        "max_questions": max(question_counts),
        "format": {
            "structure": "list of objects",
            "fields": ["label_path", "example_questions"],
            "description": "Each object contains a label path and its corresponding example questions"
        }
    }
    
    stats_file = output_file.replace('.json', '_stats.json')
    with open(stats_file, 'w', encoding='utf-8') as f:
        json.dump(stats, f, ensure_ascii=False, indent=2)
    
    print(f"📊 统计信息保存到: {stats_file}")
    
    print(f"\n✅ 重构完成！")
    print(f"📈 新格式特点:")
    print(f"  - 结构化数据: 列表包含对象")
    print(f"  - 标准字段名: label_path, example_questions")
    print(f"  - 易于处理: 适合机器学习和数据分析")
    print(f"  - 保持完整: 所有原始数据都被保留")
    
    return restructured_data, stats

def main():
    # parser = argparse.ArgumentParser(description="重构小标签数据格式")
    # parser.add_argument('--input', '-i', type=str, required=True, help='输入JSON文件路径')
    # parser.add_argument('--output', '-o', type=str, required=True, help='输出JSON文件路径')
    
    # args = parser.parse_args()
    input_file = r"D:\python_project\dify-图片转解析\ultimate_merge_small_labels_numbered.json"
    output_file = r"D:\python_project\dify-图片转解析\ultimate_merge_small_labels_numbered.json"
    # 执行重构
    restructure_small_labels(input_file, output_file)

if __name__ == "__main__":
    main()
