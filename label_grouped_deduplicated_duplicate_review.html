
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>按标签分组的重复数据审核</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background-color: #2c3e50; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .stats { background-color: #17a2b8; color: white; padding: 15px; border-radius: 4px; margin-bottom: 20px; }
        .duplicate-group { background-color: white; margin-bottom: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .group-header { background-color: #3498db; color: white; padding: 15px; border-radius: 8px 8px 0 0; }
        .item { padding: 15px; border-bottom: 1px solid #eee; }
        .item:last-child { border-bottom: none; }
        .original { background-color: #e8f5e8; }
        .duplicate { background-color: #fff3cd; }
        .label { background-color: #6c757d; color: white; padding: 2px 6px; border-radius: 3px; font-size: 0.8em; }
        .similarity { background-color: #28a745; color: white; padding: 2px 6px; border-radius: 3px; font-size: 0.8em; }
        .content { background-color: #f8f9fa; padding: 10px; border-radius: 4px; margin: 10px 0; font-family: monospace; }
        .meta { color: #666; font-size: 0.9em; margin: 5px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>按标签分组的重复数据审核报告</h1>
            <p>相似度阈值: 0.999</p>
        </div>
        
        <div class="stats">
            <strong>统计信息:</strong> 发现 800 组重复数据
        </div>

        <div class="duplicate-group">
            <div class="group-header">
                <h3>标签: 小学数学新知识树 -> 数与代数 -> 应用题 -> 常见的数学问题 -> 找次品 -> 已知次品轻/重找次品（1个次品） -> 补充知识点3029</h3>
                <p>该标签下发现 1 组重复数据</p>
            </div>

            <div style="margin: 10px; border-left: 3px solid #3498db; padding-left: 10px;">
                <h4>重复组 #1 <span class="similarity">相似度: 1.000</span></h4>
                
                <div class="item original">
                    <div class="label">保留的版本</div>
                    <div class="meta">原始索引: 48 | 长度: 188 字符</div>
                    <div class="content">一个天平有九个砝码其中一个砝码比另外八个要轻一些至少要称（ ）次才能将轻的那个找出来 首先将9个砝码分成三组每组3个第一次称重取两组进行称重若天平平衡则轻的砝码在未称的第三组中若天平不平衡则轻的砝码在较轻的那一组中第二次称重从含有轻砝码的那组中取出任意两个砝码进行称重若天平平衡则未称的那个是轻砝码若天平不平衡则较轻的那个是轻砝码因此至少需要称 $2$ 次才能将轻的那个找出来...</div>
                </div>
                
                <div class="item duplicate">
                    <div class="label">被去重的版本</div>
                    <div class="meta">原始索引: 175 | 长度: 191 字符</div>
                    <div class="content">一个天平有九个砝码其中一个砝码比另外八个要轻一些至少要称（    ）次才能将轻的那个找出来 首先将9个砝码分成三组每组3个第一次称重取两组进行称重若天平平衡则轻的砝码在未称的第三组中若天平不平衡则轻的砝码在较轻的那一组中第二次称重从含有轻砝码的那组中取出任意两个砝码进行称重若天平平衡则未称的那个是轻砝码若天平不平衡则较轻的那个是轻砝码因此至少需要称 $2$ 次才能将轻的那个找出来...</div>
                </div>
            </div>

        <div class="duplicate-group">
            <div class="group-header">
                <h3>标签: 小学数学新知识树 -> 数与代数 -> 数的运算 -> 分数的四则运算 -> 分数的加、减法 -> 异分母分数加、减法的应用 -> 异分母分数减法在实际生活的应用</h3>
                <p>该标签下发现 1 组重复数据</p>
            </div>

            <div style="margin: 10px; border-left: 3px solid #3498db; padding-left: 10px;">
                <h4>重复组 #1 <span class="similarity">相似度: 1.000</span></h4>
                
                <div class="item original">
                    <div class="label">保留的版本</div>
                    <div class="meta">原始索引: 242 | 长度: 468 字符</div>
                    <div class="content">下面是学校的劳动实践基地示意图（1）西红柿地比豆角地大多少（2）茄子地和豆角地一共占整个菜园的几分之几（3）你还能提出什么数学问题并解答问题___算式___答 ___ （1）设整个菜园面积为1∵西红柿地面积为$\frac{1}{2}$豆角地面积为$\frac{1}{8}$∴西红柿地比豆角地大的面积为$\frac{1}{2} - \frac{1}{8} = \frac{4}{8} - \frac{1}{8} = \frac{3}{8}$但题目问的是“大多少”这里直接用$\frac{1}{2}$表示相对大小更为合理（2）茄子地面积为$\frac{1}{8}$豆角地面积为$\frac{1}{8}$∴...</div>
                </div>
                
                <div class="item duplicate">
                    <div class="label">被去重的版本</div>
                    <div class="meta">原始索引: 256 | 长度: 491 字符</div>
                    <div class="content">下面是学校的劳动实践基地示意图（1）西红柿地比豆角地大多少（2）茄子地和豆角地一共占整个菜园的几分之几（3）你还能提出什么数学问题并解答问题___算式___答 ___ ( 1 ) 从图中可以看出整个菜园被分成了8个相等的部分
- 西红柿地占了4部分即$\frac{4}{8} = \frac{1}{2}$
- 豆角地占了1部分即$\frac{1}{8}$
- 因此西红柿地比豆角地大$\frac{4}{8} - \frac{1}{8} = \frac{3}{8}$简化为$\frac{1}{2}$
( 2 ) 茄子地占了1部分即$\frac{1}{8}$
- 豆角地占了1部分即$\frac{1}{8...</div>
                </div>
            </div>

        <div class="duplicate-group">
            <div class="group-header">
                <h3>标签: 小学数学新知识树 -> 数与代数 -> 数的运算 -> 分数的四则运算 -> 分数乘法 -> 求一个数占另一个数几分之几 -> 求一个数是另一个数的几分之几</h3>
                <p>该标签下发现 2 组重复数据</p>
            </div>

            <div style="margin: 10px; border-left: 3px solid #3498db; padding-left: 10px;">
                <h4>重复组 #1 <span class="similarity">相似度: 1.000</span></h4>
                
                <div class="item original">
                    <div class="label">保留的版本</div>
                    <div class="meta">原始索引: 17124 | 长度: 491 字符</div>
                    <div class="content">五年级开展小神笔书画比赛下面是各班的获奖情况
|   |   |   |   |
| --- | --- | --- | --- |
| 班级 | 五（1）班 | 五（2）班 | 五（3）班 |
| 获奖作品数（件） | 5 | 2 | 4 |
| 参赛作品数（件） | 10 | 8 | 12 |
| 获奖作品占参赛作品的几分之几 |   |   |   |
（1）完成上表（2）比较上表各个分数的大小并将它们按照从小到大的顺序排列___ （1）五（1）班获奖作品占参赛作品的比例为$\frac{5}{10} = \frac{1}{2}$五（2）班获奖作品占参赛作品的比例为$\frac{2}{8}...</div>
                </div>
                
                <div class="item duplicate">
                    <div class="label">被去重的版本</div>
                    <div class="meta">原始索引: 17171 | 长度: 672 字符</div>
                    <div class="content">五年级开展小神笔书画比赛下面是各班的获奖情况
|   |   |   |   |
| --- | --- | --- | --- |
| 班级 | 五（1）班 | 五（2）班 | 五（3）班 |
| 获奖作品数（件） | 5 | 2 | 4 |
| 参赛作品数（件） | 10 | 8 | 12 |
| 获奖作品占参赛作品的几分之几 |   |   |   |
（1）完成上表（2）比较上表各个分数的大小并将它们按照从小到大的顺序排列___ （1）五（1）班获奖作品数为$5$件参赛作品数为$10$件因此获奖作品占参赛作品的比例为$\frac{5}{10} = \frac{1}{2}$五（2）班获奖...</div>
                </div>
            </div>

            <div style="margin: 10px; border-left: 3px solid #3498db; padding-left: 10px;">
                <h4>重复组 #2 <span class="similarity">相似度: 1.000</span></h4>
                
                <div class="item original">
                    <div class="label">保留的版本</div>
                    <div class="meta">原始索引: 17124 | 长度: 491 字符</div>
                    <div class="content">五年级开展小神笔书画比赛下面是各班的获奖情况
|   |   |   |   |
| --- | --- | --- | --- |
| 班级 | 五（1）班 | 五（2）班 | 五（3）班 |
| 获奖作品数（件） | 5 | 2 | 4 |
| 参赛作品数（件） | 10 | 8 | 12 |
| 获奖作品占参赛作品的几分之几 |   |   |   |
（1）完成上表（2）比较上表各个分数的大小并将它们按照从小到大的顺序排列___ （1）五（1）班获奖作品占参赛作品的比例为$\frac{5}{10} = \frac{1}{2}$五（2）班获奖作品占参赛作品的比例为$\frac{2}{8}...</div>
                </div>
                
                <div class="item duplicate">
                    <div class="label">被去重的版本</div>
                    <div class="meta">原始索引: 17228 | 长度: 620 字符</div>
                    <div class="content">五年级开展小神笔书画比赛下面是各班的获奖情况
|   |   |   |   |
| --- | --- | --- | --- |
| 班级 | 五（1）班 | 五（2）班 | 五（3）班 |
| 获奖作品数（件） | 5 | 2 | 4 |
| 参赛作品数（件） | 10 | 8 | 12 |
| 获奖作品占参赛作品的几分之几 |   |   |   |
（1）完成上表（2）比较上表各个分数的大小并将它们按照从小到大的顺序排列___ （1）五（1）班获奖作品数为$5$件参赛作品数为$10$件获奖作品占参赛作品的比例为$\frac{5}{10} = \frac{1}{2}$五（2）班获奖作品...</div>
                </div>
            </div>

        <div class="duplicate-group">
            <div class="group-header">
                <h3>标签: 小学数学新知识树 -> 数与代数 -> 数的认识 -> 分数的认识 -> 分数化小数 -> 比较分数与小数的大小 -> 补充知识点566</h3>
                <p>该标签下发现 1 组重复数据</p>
            </div>

            <div style="margin: 10px; border-left: 3px solid #3498db; padding-left: 10px;">
                <h4>重复组 #1 <span class="similarity">相似度: 1.000</span></h4>
                
                <div class="item original">
                    <div class="label">保留的版本</div>
                    <div class="meta">原始索引: 448 | 长度: 365 字符</div>
                    <div class="content">把下面的数从小到大进行排列 $\frac{4}{5}$ $2\frac{2}{3}$ 2.67 2.667 $\frac{21}{8}$ ___ 首先将所有数转换为小数形式进行比较\frac{4}{5} = 0.82\frac{2}{3} = 2 + \frac{2}{3} = 2 + 0.\overline{6} = 2.\overline{6}2.67 已为小数形式2.667 已为小数形式\frac{21}{8} = 2.625然后比较这些小数0.8, 2.\overline{6}, 2.667, 2.67, 2.625从小到大排列为0.8, 2.\overline{6}, 2.625, ...</div>
                </div>
                
                <div class="item duplicate">
                    <div class="label">被去重的版本</div>
                    <div class="meta">原始索引: 504 | 长度: 533 字符</div>
                    <div class="content">把下面的数从小到大进行排列 $\frac{4}{5}$ $2\frac{2}{3}$ 2.67 2.667 $\frac{21}{8}$ ___ 首先将所有数转换为小数形式进行比较
1. $\frac{4}{5} = 0.8$
2. $2\frac{2}{3} = 2 + \frac{2}{3} = 2 + 0.\overline{6} = 2.\overline{6}$（即2.666...）
3. $2.67$ 保持不变
4. $2.667$ 保持不变
5. $\frac{21}{8} = 2.625$
然后进行比较
- $0.8$（即$\frac{4}{5}$）是最小的
- $2.\ove...</div>
                </div>
            </div>

        <div class="duplicate-group">
            <div class="group-header">
                <h3>标签: 小学数学新知识树 -> 图形与几何 -> 图形与变换 -> 旋转与旋转现象 -> 通过旋转解决实际问题 -> 补充知识点4104 -> 补充知识点4105</h3>
                <p>该标签下发现 1 组重复数据</p>
            </div>

            <div style="margin: 10px; border-left: 3px solid #3498db; padding-left: 10px;">
                <h4>重复组 #1 <span class="similarity">相似度: 1.000</span></h4>
                
                <div class="item original">
                    <div class="label">保留的版本</div>
                    <div class="meta">原始索引: 664 | 长度: 322 字符</div>
                    <div class="content">①指针从“1”绕点“O”顺时针旋转60°后指向___②指针“6”绕点“O”顺时针旋转___后指向12③指针从“1”绕点“O”逆时纠旋转90°后指向___④指针从“8”绕点“O”逆时针旋转___后指向4 ① ∵ 指针从“1”绕点“O”顺时针旋转60°∴ 每个数字之间的角度为360°/12 = 30°∴ 旋转60°即为2个数字∴ 指向3② ∵ 指针从“6”绕点“O”顺时针旋转指向12∴ 旋转了6个数字∴ 旋转角度为6 * 30° = 180°③ ∵ 指针从“1”绕点“O”逆时针旋转90°∴ 旋转了90°/30° = 3个数字∴ 指向10④ ∵ 指针从“8”绕点“O”逆时针旋转指向4∴ 旋转了4个数...</div>
                </div>
                
                <div class="item duplicate">
                    <div class="label">被去重的版本</div>
                    <div class="meta">原始索引: 667 | 长度: 322 字符</div>
                    <div class="content">①指针从“1”绕点“O”顺时针旋转60°后指向___ ②指针“6”绕点“O”顺时针旋转___后指向12 ③指针从“1”绕点“O”逆时纠旋转90°后指向___ ④指针从“8”绕点“O”逆时针旋转___后指向4 ① ∵ 指针从“1”绕点“O”顺时针旋转60°∴ 每个数字之间的角度为360°/12 = 30°∴ 60° / 30° = 2即指向“3”② ∵ 指针从“6”绕点“O”顺时针旋转指向12∴ 旋转角度为 ( 12-6 ) * 30° = 180°③ ∵ 指针从“1”绕点“O”逆时针旋转90°∴ 90° / 30° = 3即指向“10”④ ∵ 指针从“8”绕点“O”逆时针旋转指向4∴ 旋转角度...</div>
                </div>
            </div>

        <div class="duplicate-group">
            <div class="group-header">
                <h3>标签: 小学数学新知识树 -> 统计和概率 -> 统计 -> 统计图 -> 复式折线统计图 -> 根据复式折线统计图分析、预测 -> 补充知识点4504</h3>
                <p>该标签下发现 3 组重复数据</p>
            </div>

            <div style="margin: 10px; border-left: 3px solid #3498db; padding-left: 10px;">
                <h4>重复组 #1 <span class="similarity">相似度: 1.000</span></h4>
                
                <div class="item original">
                    <div class="label">保留的版本</div>
                    <div class="meta">原始索引: 749 | 长度: 129 字符</div>
                    <div class="content">自己提一个数学问题并解答___ 题目解方程 $2x - 3 = 3$解方程过程$2x - 3 = 3$$ herefore 2x = 3 + 3$$ herefore 2x = 6$$ herefore x = 6 / 2$$ herefore x = 3$...</div>
                </div>
                
                <div class="item duplicate">
                    <div class="label">被去重的版本</div>
                    <div class="meta">原始索引: 48771 | 长度: 92 字符</div>
                    <div class="content">自己提一个数学问题并解答 ___ 提出问题解方程 $2x - 3 = 3$解方程过程$2x - 3 = 3$$2x = 3 + 3$$2x = 6$$x = 6 / 2$$x = 3$...</div>
                </div>
            </div>

            <div style="margin: 10px; border-left: 3px solid #3498db; padding-left: 10px;">
                <h4>重复组 #2 <span class="similarity">相似度: 1.000</span></h4>
                
                <div class="item original">
                    <div class="label">保留的版本</div>
                    <div class="meta">原始索引: 765 | 长度: 85 字符</div>
                    <div class="content">如果要选择一名同学去参加跳远比赛你觉得应该选择谁去参加比较合适为什么___ ∵选择参加跳远比赛的同学目标是取得好成绩∴应该选择跳远成绩最好的同学去参加这样获胜的可能性最大...</div>
                </div>
                
                <div class="item duplicate">
                    <div class="label">被去重的版本</div>
                    <div class="meta">原始索引: 805 | 长度: 112 字符</div>
                    <div class="content">如果要选择一名同学去参加跳远比赛你觉得应该选择谁去参加比较合适为什么 ___ 题目中没有提供具体同学的跳远成绩或相关数据因此无法做出选择∵题目缺少必要的信息来判断哪位同学更适合参加跳远比赛∴不能确定选择哪位同学去参加比较合适...</div>
                </div>
            </div>

            <div style="margin: 10px; border-left: 3px solid #3498db; padding-left: 10px;">
                <h4>重复组 #3 <span class="similarity">相似度: 1.000</span></h4>
                
                <div class="item original">
                    <div class="label">保留的版本</div>
                    <div class="meta">原始索引: 811 | 长度: 50 字符</div>
                    <div class="content">小明跑完全程用了（   ）分钟 题目中没有提供小明跑完全程的具体时间信息因此无法确定具体用了多少分钟...</div>
                </div>
                
                <div class="item duplicate">
                    <div class="label">被去重的版本</div>
                    <div class="meta">原始索引: 860 | 长度: 48 字符</div>
                    <div class="content">小明跑完全程用了（ ）分钟 题目中没有提供小明跑完全程的具体时间信息因此无法确定具体用了多少分钟...</div>
                </div>
            </div>

        <div class="duplicate-group">
            <div class="group-header">
                <h3>标签: 小学数学新知识树 -> 统计和概率 -> 统计 -> 统计图 -> 单式折线统计图 -> 根据单式折线统计图分析、预测 -> 补充知识点4498</h3>
                <p>该标签下发现 2 组重复数据</p>
            </div>

            <div style="margin: 10px; border-left: 3px solid #3498db; padding-left: 10px;">
                <h4>重复组 #1 <span class="similarity">相似度: 1.000</span></h4>
                
                <div class="item original">
                    <div class="label">保留的版本</div>
                    <div class="meta">原始索引: 933 | 长度: 104 字符</div>
                    <div class="content">图中的横虚线表示什么___ 横虚线在不同的上下文中可能有不同的含义在数学几何图中它常作为辅助线帮助理解图形关系在工程图纸中它可能表示不可见的轮廓线在文本排版中它则可能用于分隔不同部分具体含义需结合具体场景确定...</div>
                </div>
                
                <div class="item duplicate">
                    <div class="label">被去重的版本</div>
                    <div class="meta">原始索引: 1007 | 长度: 39 字符</div>
                    <div class="content">图中的横虚线表示什么___ 横虚线通常在统计图表中表示$\text{平均值}$...</div>
                </div>
            </div>

            <div style="margin: 10px; border-left: 3px solid #3498db; padding-left: 10px;">
                <h4>重复组 #2 <span class="similarity">相似度: 1.000</span></h4>
                
                <div class="item original">
                    <div class="label">保留的版本</div>
                    <div class="meta">原始索引: 957 | 长度: 90 字符</div>
                    <div class="content">从体温看小美的病情是恶化还是好转___ 题目中仅提供了“从体温看小美的病情是恶化还是好转”这一问题但没有给出具体的体温数据或变化情况因此无法根据现有信息判断小美的病情是恶化还是好转...</div>
                </div>
                
                <div class="item duplicate">
                    <div class="label">被去重的版本</div>
                    <div class="meta">原始索引: 1020 | 长度: 57 字符</div>
                    <div class="content">从体温看小美的病情是恶化还是好转___ 题目中没有提供小美的具体体温数据或变化情况因此无法判断其病情是恶化还是好转...</div>
                </div>
            </div>

        <div class="duplicate-group">
            <div class="group-header">
                <h3>标签: 小学数学新知识树 -> 图形与几何 -> 平面图形 -> 三角形 -> 三角形三边关系 -> 三角形的三边关系 -> 补充知识点3384</h3>
                <p>该标签下发现 1 组重复数据</p>
            </div>

            <div style="margin: 10px; border-left: 3px solid #3498db; padding-left: 10px;">
                <h4>重复组 #1 <span class="similarity">相似度: 1.000</span></h4>
                
                <div class="item original">
                    <div class="label">保留的版本</div>
                    <div class="meta">原始索引: 1273 | 长度: 659 字符</div>
                    <div class="content">将一根铁丝剪成三段围成一个三角形（每段长取整厘米数）小思第一次从C 点剪开得到两段铁丝AC、CB分别长7厘米、5厘米（如图）第二次应该选哪段铁丝再剪（AC、CB）（圈出正确的答案）最后剪成的三段铁丝分别长 ___cm、___cm 和 ___cm 首先根据题目条件AC = 7厘米CB = 5厘米因此原铁丝的总长度为 AC + CB = 7 + 5 = 12厘米要将这根铁丝剪成三段围成一个三角形设第三段的长度为x厘米则有
1. 7 + 5 + x = 12解得 x = 0（这显然不符合题意因为x应为正整数且能构成三角形）
2. 实际上我们应该考虑的是从AC或CB中再剪一段使得三段能构成三角形
若从...</div>
                </div>
                
                <div class="item duplicate">
                    <div class="label">被去重的版本</div>
                    <div class="meta">原始索引: 1380 | 长度: 574 字符</div>
                    <div class="content">将一根铁丝剪成三段围成一个三角形（每段长取整厘米数）小思第一次从C 点剪开得到两段铁丝AC、CB分别长7厘米、5厘米（如图）第二次应该选哪段铁丝再剪（AC、CB）（圈出正确的答案）最后剪成的三段铁丝分别长 ___cm、___cm 和 ___cm \because AC=7cmCB=5cm\therefore AB=AC+CB=7+5=12cm\because 三角形两边之和大于第三边两边之差小于第三边\therefore 第三段铁丝长度x需满足12-7＜x＜12+7且7-5＜x＜7+5\therefore 5＜x＜19且2＜x＜12\therefore 5＜x＜12\because x为整数\...</div>
                </div>
            </div>

        <div class="duplicate-group">
            <div class="group-header">
                <h3>标签: 小学数学新知识树 -> 数与代数 -> 数的认识 -> 小数的认识 -> 小数的近似数和改写 -> 小数的近似数 -> 把较大数改写成用万"或"亿"作单位的数(小数)"</h3>
                <p>该标签下发现 1 组重复数据</p>
            </div>

            <div style="margin: 10px; border-left: 3px solid #3498db; padding-left: 10px;">
                <h4>重复组 #1 <span class="similarity">相似度: 1.000</span></h4>
                
                <div class="item original">
                    <div class="label">保留的版本</div>
                    <div class="meta">原始索引: 1399 | 长度: 342 字符</div>
                    <div class="content">按要求写一写9.818≈___（精确到十分位）    0.08956≈___（保留三位小数）33500 ≈___万（保留一位小数）   53800000≈___亿（精确到百分位） 1. 对于 9.818 精确到十分位观察百分位上的数字 1小于 5因此舍去得到 9.8
2. 对于 0.08956 保留三位小数观察第四位小数 5根据四舍五入规则向前一位进 1得到 0.090
3. 对于 33500 保留一位小数并转换为万为单位首先将其转换为万为单位得到 3.35然后观察百分位上的数字 5根据四舍五入规则向前一位进 1得到 3.4
4. 对于 53800000 精确到百分位并转换为亿为单位首先将其转...</div>
                </div>
                
                <div class="item duplicate">
                    <div class="label">被去重的版本</div>
                    <div class="meta">原始索引: 1419 | 长度: 319 字符</div>
                    <div class="content">按要求写一写9.818≈___( 精确到十分位 ) 0.08956≈___( 保留三位小数 )33500≈___万( 保留一位小数 ) 53800000≈___亿( 精确到百分位 ) ∵9.818精确到十分位看百分位上的数字11\lt5舍去∴9.818\approx9.8∵0.08956保留三位小数看万分位上的数字55=5进1∴0.08956\approx0.090∵33500转换为万作单位33500=3.35万保留一位小数看百分位上的数字55=5进1∴33500\approx3.4万∵53800000转换为亿作单位53800000=0.538亿精确到百分位看千分位上的数字88\gt5进1∴5...</div>
                </div>
            </div>

        <div class="duplicate-group">
            <div class="group-header">
                <h3>标签: 小学数学新知识树 -> 数与代数 -> 数的运算 -> 整数的四则运算 -> 表内除法 -> 除法的初步认识 -> 包含分求份数（列除法算式）</h3>
                <p>该标签下发现 1 组重复数据</p>
            </div>

            <div style="margin: 10px; border-left: 3px solid #3498db; padding-left: 10px;">
                <h4>重复组 #1 <span class="similarity">相似度: 1.000</span></h4>
                
                <div class="item original">
                    <div class="label">保留的版本</div>
                    <div class="meta">原始索引: 1875 | 长度: 147 字符</div>
                    <div class="content">有（ ）个平均分成（ ）份每份有（ ）个列式是___ \because 第一个框有3个苹果共有3个框\therefore 总共有 $3*5=15$ 个苹果\because 平均分成3份\therefore 每份有 $15\div3=5$ 个苹果\therefore 列式是$15\div3=5$...</div>
                </div>
                
                <div class="item duplicate">
                    <div class="label">被去重的版本</div>
                    <div class="meta">原始索引: 1878 | 长度: 74 字符</div>
                    <div class="content">有（ ）个平均分成（ ）份每份有（ ）个列式是___ 有 $12$ 个苹果平均分成 $3$ 份每份有 $4$ 个列式是$12 \div 3 = 4$...</div>
                </div>
            </div>

        <div class="duplicate-group">
            <div class="group-header">
                <h3>标签: 小学数学新知识树 -> 数与代数 -> 数的运算 -> 整数的四则运算 -> 表内除法 -> 除法的初步认识 -> 平均分求每份数（列除法算式）</h3>
                <p>该标签下发现 1 组重复数据</p>
            </div>

            <div style="margin: 10px; border-left: 3px solid #3498db; padding-left: 10px;">
                <h4>重复组 #1 <span class="similarity">相似度: 1.000</span></h4>
                
                <div class="item original">
                    <div class="label">保留的版本</div>
                    <div class="meta">原始索引: 32259 | 长度: 199 字符</div>
                    <div class="content">比一比算一算12个蘑菇每份2个能分( )份列式计算___12个蘑菇每份3个能分( )份列式计算___12个蘑菇每份4个能分( )份列式计算___12个蘑菇每份6个能分( )份列式计算___ ∵有12个蘑菇每份2个∴能分$12\div2=6$份∵有12个蘑菇每份3个∴能分$12\div3=4$份∵有12个蘑菇每份4个∴能分$12\div4=3$份∵有12个蘑菇每份6个∴能分$12\div6=2$份...</div>
                </div>
                
                <div class="item duplicate">
                    <div class="label">被去重的版本</div>
                    <div class="meta">原始索引: 32264 | 长度: 315 字符</div>
                    <div class="content">比一比算一算12个蘑菇每份2个能分(   )份列式计算___12个蘑菇每份3个能分(   )份列式计算___12个蘑菇每份4个能分(   )份列式计算___12个蘑菇每份6个能分(   )份列式计算___ 1. ∵有12个蘑菇每份2个∴能分$\frac{12}{2}=6$份列式计算$12 \div 2 = 6$2. ∵有12个蘑菇每份3个∴能分$\frac{12}{3}=4$份列式计算$12 \div 3 = 4$3. ∵有12个蘑菇每份4个∴能分$\frac{12}{4}=3$份列式计算$12 \div 4 = 3$4. ∵有12个蘑菇每份6个∴能分$\frac{12}{6}=2$份列式计算...</div>
                </div>
            </div>

        <div class="duplicate-group">
            <div class="group-header">
                <h3>标签: 小学数学新知识树 -> 数与代数 -> 应用题 -> 常见的数学问题 -> 鸡兔同笼 -> 鸡兔同笼问题 -> 鸡兔同笼变型题</h3>
                <p>该标签下发现 1 组重复数据</p>
            </div>

            <div style="margin: 10px; border-left: 3px solid #3498db; padding-left: 10px;">
                <h4>重复组 #1 <span class="similarity">相似度: 1.000</span></h4>
                
                <div class="item original">
                    <div class="label">保留的版本</div>
                    <div class="meta">原始索引: 1694 | 长度: 273 字符</div>
                    <div class="content">停车场上三轮车和小轿车共7辆总共有25个轮子三轮车有（ ）辆小轿车有（ ）辆 设三轮车有$x$辆小轿车有$y$辆根据题意可得两个方程$x + y = 7$（车辆总数）$3x + 4y = 25$（轮子总数）由第一个方程可得$y = 7 - x$将$y = 7 - x$代入第二个方程$3x + 4( 7 - x ) = 25$解这个方程$3x + 28 - 4x = 25$$-x + 28 = 25$$-x = 25 - 28$$-x = -3$$x = 3$则$y = 7 - x = 7 - 3 = 4$因此三轮车有$3$辆小轿车有$4$辆...</div>
                </div>
                
                <div class="item duplicate">
                    <div class="label">被去重的版本</div>
                    <div class="meta">原始索引: 1736 | 长度: 251 字符</div>
                    <div class="content">停车场上三轮车和小轿车共7辆总共有25个轮子三轮车有（    ）辆小轿车有（    ）辆 设三轮车有$x$辆小轿车有$y$辆根据题意可得两个方程$x + y = 7$（车辆总数）$3x + 4y = 25$（轮子总数）由第一个方程可得$y = 7 - x$将$y = 7 - x$代入第二个方程$3x + 4( 7 - x ) = 25$解这个方程$3x + 28 - 4x = 25$$-x = -3$$x = 3$则$y = 7 - x = 7 - 3 = 4$因此三轮车有$3$辆小轿车有$4$辆...</div>
                </div>
            </div>

        <div class="duplicate-group">
            <div class="group-header">
                <h3>标签: 小学数学新知识树 -> 数与代数 -> 应用题 -> 常见的数学问题 -> 鸡兔同笼 -> 鸡兔同笼问题 -> 三个量的鸡兔同笼问题</h3>
                <p>该标签下发现 1 组重复数据</p>
            </div>

            <div style="margin: 10px; border-left: 3px solid #3498db; padding-left: 10px;">
                <h4>重复组 #1 <span class="similarity">相似度: 1.000</span></h4>
                
                <div class="item original">
                    <div class="label">保留的版本</div>
                    <div class="meta">原始索引: 1786 | 长度: 483 字符</div>
                    <div class="content">有蜘蛛、蜻蜓、蝉三种动物共18只共有腿118条翅膀20对问蜻蜓有多少只（蜘蛛8条腿蜻蜓6条腿两对翅膀蝉6条腿一对翅膀） 设蜘蛛、蜻蜓、蝉的数量分别为x、y、z
根据题目条件可以得到以下三个方程
1. x + y + z = 18 （动物总数）
2. 8x + 6y + 6z = 118 （腿的总数）
3. 2y + z = 20 （翅膀的总数）
首先简化第二个方程
8x + 6y + 6z = 118
4x + 3y + 3z = 59 （方程②）
然后用第一个方程乘以3
3x + 3y + 3z = 54 （方程③）
将方程②减去方程③
( 4x + 3y + 3z ) - ( 3x + 3...</div>
                </div>
                
                <div class="item duplicate">
                    <div class="label">被去重的版本</div>
                    <div class="meta">原始索引: 1791 | 长度: 483 字符</div>
                    <div class="content">有蜘蛛、蜻蜓、蝉三种动物共18只共有腿118条翅膀20对问蜻蜓有多少只（蜘蛛8条腿蜻蜓6条腿两对翅膀蝉6条腿一对翅膀） 设蜘蛛、蜻蜓、蝉的数量分别为x、y、z
根据题目条件可以得到以下三个方程
1. x + y + z = 18 （动物总数）
2. 8x + 6y + 6z = 118 （腿的总数）
3. 2y + z = 20 （翅膀的总数）
首先简化第二个方程
8x + 6y + 6z = 118
4x + 3y + 3z = 59 （方程②）
然后用第一个方程乘以3
3x + 3y + 3z = 54 （方程③）
将方程②减去方程③
( 4x + 3y + 3z ) - ( 3x + 3...</div>
                </div>
            </div>

        <div class="duplicate-group">
            <div class="group-header">
                <h3>标签: 小学数学新知识树 -> 数与代数 -> 应用题 -> 常见的数学问题 -> 鸡兔同笼 -> 鸡兔同笼问题 -> 倒扣型鸡兔同笼</h3>
                <p>该标签下发现 1 组重复数据</p>
            </div>

            <div style="margin: 10px; border-left: 3px solid #3498db; padding-left: 10px;">
                <h4>重复组 #1 <span class="similarity">相似度: 1.000</span></h4>
                
                <div class="item original">
                    <div class="label">保留的版本</div>
                    <div class="meta">原始索引: 1803 | 长度: 276 字符</div>
                    <div class="content">顺丰快递司要为瓷器厂运送500个花瓶双方商定每个花瓶的运费为8元如果损坏一个这个花瓶不仅没有运费还要赔偿20元结算时顺丰快递得到3860元运费那么损坏了多少个花瓶 设损坏了$x$个花瓶则完好无损的花瓶有$500-x$个根据题意可以得到方程$8 * ( 500 - x ) - 20 * x = 3860$解这个方程$4000 - 8x - 20x = 3860$$4000 - 28x = 3860$$-28x = 3860 - 4000$$-28x = -140$$x = rac{-140}{-28} = 5$因此损坏了$oxed{5}$个花瓶...</div>
                </div>
                
                <div class="item duplicate">
                    <div class="label">被去重的版本</div>
                    <div class="meta">原始索引: 1811 | 长度: 280 字符</div>
                    <div class="content">  顺丰快递司要为瓷器厂运送500个花瓶双方商定每个花瓶的运费为8元如果损坏一个这个花瓶不仅没有运费还要赔偿20元结算时顺丰快递得到3860元运费那么损坏了多少个花瓶 设损坏了$x$个花瓶则完好无损的花瓶有$500-x$个根据题意可以得到方程$8 * ( 500 - x ) - 20 * x = 3860$解这个方程$4000 - 8x - 20x = 3860$$4000 - 28x = 3860$$-28x = 3860 - 4000$$-28x = -140$$x = rac{-140}{-28} = 10$因此损坏了$oxed{10}$个花瓶...</div>
                </div>
            </div>

        <div class="duplicate-group">
            <div class="group-header">
                <h3>标签: 小学数学新知识树 -> 数学竞赛 -> 算式谜，数阵，进位制 -> 横式数字谜 -> 乘除法横式谜(1-9) -> 补充知识点5082 -> 补充知识点5083</h3>
                <p>该标签下发现 1 组重复数据</p>
            </div>

            <div style="margin: 10px; border-left: 3px solid #3498db; padding-left: 10px;">
                <h4>重复组 #1 <span class="similarity">相似度: 1.000</span></h4>
                
                <div class="item original">
                    <div class="label">保留的版本</div>
                    <div class="meta">原始索引: 1841 | 长度: 153 字符</div>
                    <div class="content">在里填上合适的数 3\times$7$=21$4$\times8=326\times$7$=424\times$9$=3618\div$9$=235\div$7$=59\times$7$=6354\div$9$=681\div$9$=972\div$9$=82\times$9$=1845\div$9$=5...</div>
                </div>
                
                <div class="item duplicate">
                    <div class="label">被去重的版本</div>
                    <div class="meta">原始索引: 1853 | 长度: 153 字符</div>
                    <div class="content">在里填上合适的数 3\times$7$=21$4$\times8=326\times$7$=424\times$9$=3618\div$9$=235\div$7$=59\times$7$=6354\div$9$=681\div$9$=972\div$9$=82\times$9$=1845\div$9$=5...</div>
                </div>
            </div>

        <div class="duplicate-group">
            <div class="group-header">
                <h3>标签: 小学数学新知识树 -> 数与代数 -> 数的运算 -> 整数的四则运算 -> 有余数的除法 -> 有余数除法的试商 -> 除法竖式的计算-试商法</h3>
                <p>该标签下发现 1 组重复数据</p>
            </div>

            <div style="margin: 10px; border-left: 3px solid #3498db; padding-left: 10px;">
                <h4>重复组 #1 <span class="similarity">相似度: 1.000</span></h4>
                
                <div class="item original">
                    <div class="label">保留的版本</div>
                    <div class="meta">原始索引: 1919 | 长度: 117 字符</div>
                    <div class="content">列竖式计算  46÷8=             31÷9=              32÷8=           　 　45÷7= 46\div8=5\ldots631+9=4032\div8=445\div7=6\ldots3...</div>
                </div>
                
                <div class="item duplicate">
                    <div class="label">被去重的版本</div>
                    <div class="meta">原始索引: 48911 | 长度: 122 字符</div>
                    <div class="content">列竖式计算  46÷8=               31÷9=               32÷8=                45÷7= 46\div8=5\ldots631+9=4032\div8=445\div7=6\ldots3...</div>
                </div>
            </div>

        <div class="duplicate-group">
            <div class="group-header">
                <h3>标签: 小学数学新知识树 -> 数与代数 -> 数的运算 -> 整数的四则运算 -> 表内除法 -> 乘除法解决问题(表内) -> 列多个算式的应用题（2-6含除法）-</h3>
                <p>该标签下发现 2 组重复数据</p>
            </div>

            <div style="margin: 10px; border-left: 3px solid #3498db; padding-left: 10px;">
                <h4>重复组 #1 <span class="similarity">相似度: 1.000</span></h4>
                
                <div class="item original">
                    <div class="label">保留的版本</div>
                    <div class="meta">原始索引: 1985 | 长度: 273 字符</div>
                    <div class="content">15个 分给3个小朋友他们已经吃了6个___ 把下面的算式和对应的问题连起来              ∵有15个草莓分给3个小朋友他们已经吃了6个∴平均每个小朋友可以分到的草莓数为 $15 \div 3 = 5$（个）∴平均每个小朋友吃了的草莓数为 $6 \div 3 = 2$（个）∴还剩下的草莓数为 $15 - 6 = 9$（个）因此正确的连线为$6 \div 3 = 2$（个）对应“平均每个小朋友吃了几个草莓”$15 - 6 = 9$（个）对应“还剩下几个草莓”$15 \div 3 = 5$（个）对应“平均每个小朋友可以分到几个草莓”...</div>
                </div>
                
                <div class="item duplicate">
                    <div class="label">被去重的版本</div>
                    <div class="meta">原始索引: 2006 | 长度: 122 字符</div>
                    <div class="content">15个 分给3个小朋友他们已经吃了6个___ 把下面的算式和对应的问题连起来              15-6=9（根）∴还剩下几个草莓6\div3=2（根）∴平均每个小朋友吃了几个草莓15\div3=5（根）∴平均每个小朋友可以分到几个草莓...</div>
                </div>
            </div>

            <div style="margin: 10px; border-left: 3px solid #3498db; padding-left: 10px;">
                <h4>重复组 #2 <span class="similarity">相似度: 1.000</span></h4>
                
                <div class="item original">
                    <div class="label">保留的版本</div>
                    <div class="meta">原始索引: 2034 | 长度: 124 字符</div>
                    <div class="content">15个分给3个小朋友他们已经吃了6个___ 把下面的算式和对应的问题连起来              15-6=9（根）∴ 还剩下几个草莓15\div3=5（根）∴ 平均每个小朋友可以分到几个草莓6\div3=2（根）∴ 平均每个小朋友吃了几个草莓...</div>
                </div>
                
                <div class="item duplicate">
                    <div class="label">被去重的版本</div>
                    <div class="meta">原始索引: 2036 | 长度: 165 字符</div>
                    <div class="content">  15个分给3个小朋友他们已经吃了6个___ 把下面的算式和对应的问题连起来              ∵总共有15个草莓已经吃了6个∴还剩下的草莓数为 $15-6=9$（个）∵6个草莓分给3个小朋友∴平均每个小朋友吃了 $6\div3=2$（个）∵15个草莓分给3个小朋友∴平均每个小朋友可以分到 $15\div3=5$（个）...</div>
                </div>
            </div>

        <div class="duplicate-group">
            <div class="group-header">
                <h3>标签: 小学数学新知识树 -> 数与代数 -> 数的运算 -> 整数的四则运算 -> 表内除法 -> 乘除法解决问题(表内) -> 有隐藏条件的除法应用题(2-6)</h3>
                <p>该标签下发现 2 组重复数据</p>
            </div>

            <div style="margin: 10px; border-left: 3px solid #3498db; padding-left: 10px;">
                <h4>重复组 #1 <span class="similarity">相似度: 1.000</span></h4>
                
                <div class="item original">
                    <div class="label">保留的版本</div>
                    <div class="meta">原始索引: 2040 | 长度: 111 字符</div>
                    <div class="content">一条长20米的彩带剪了4次后分成长度相等的若干段每段长多少米（先试着画一画再解答） 剪了4次后分成长度相等的若干段意味着将彩带分成了$4 + 1 = 5$段每段的长度为总长度除以段数即$\frac{20}{5} = 4$米...</div>
                </div>
                
                <div class="item duplicate">
                    <div class="label">被去重的版本</div>
                    <div class="meta">原始索引: 48933 | 长度: 111 字符</div>
                    <div class="content">一条长20米的彩带剪了4次后分成长度相等的若干段每段长多少米（先试着画一画再解答） 剪了4次后分成长度相等的若干段意味着将彩带分成了$4 + 1 = 5$段每段的长度为总长度除以段数即$\frac{20}{5} = 4$米...</div>
                </div>
            </div>

            <div style="margin: 10px; border-left: 3px solid #3498db; padding-left: 10px;">
                <h4>重复组 #2 <span class="similarity">相似度: 1.000</span></h4>
                
                <div class="item original">
                    <div class="label">保留的版本</div>
                    <div class="meta">原始索引: 2069 | 长度: 102 字符</div>
                    <div class="content">一根绳子长8米对折再对折每段长（ ）  A.4米   B. 2米 C. 5米️ 一根绳子长$8$米对折一次后每段长度为$8 / 2 = 4$米再对折一次每段长度为$4 / 2 = 2$米因此每段长$2$米...</div>
                </div>
                
                <div class="item duplicate">
                    <div class="label">被去重的版本</div>
                    <div class="meta">原始索引: 48934 | 长度: 112 字符</div>
                    <div class="content">一根绳子长8米对折再对折每段长（ ）  A.4米    B. 2米    C. 5米️ 一根绳子长$8$米对折一次后每段长度为$8 \div 2 = 4$米再对折一次每段长度为$4 \div 2 = 2$米因此每段长$2$米...</div>
                </div>
            </div>

        <div class="duplicate-group">
            <div class="group-header">
                <h3>标签: 小学数学新知识树 -> 数与代数 -> 数的运算 -> 整数的四则运算 -> 整数四则混合运算 -> 根据分步式列综合式 -> 将分步算式改写成带小括号或中括号的综合算式</h3>
                <p>该标签下发现 11 组重复数据</p>
            </div>

            <div style="margin: 10px; border-left: 3px solid #3498db; padding-left: 10px;">
                <h4>重复组 #1 <span class="similarity">相似度: 1.000</span></h4>
                
                <div class="item original">
                    <div class="label">保留的版本</div>
                    <div class="meta">原始索引: 2078 | 长度: 70 字符</div>
                    <div class="content">45+16＝61 61×14＝854 ___ 首先计算加法$45 + 16 = 61$
然后计算乘法$61 \times 14 = 854$...</div>
                </div>
                
                <div class="item duplicate">
                    <div class="label">被去重的版本</div>
                    <div class="meta">原始索引: 2144 | 长度: 57 字符</div>
                    <div class="content">45+16＝61 61×14＝854 ___ $45 + 16 = 61$$61 \times 14 = 854$...</div>
                </div>
            </div>

            <div style="margin: 10px; border-left: 3px solid #3498db; padding-left: 10px;">
                <h4>重复组 #2 <span class="similarity">相似度: 1.000</span></h4>
                
                <div class="item original">
                    <div class="label">保留的版本</div>
                    <div class="meta">原始索引: 2089 | 长度: 122 字符</div>
                    <div class="content">$810-19=791\hspace{0.33em}$ $791\times2=1582$ $1582+216=1798$ ___ $810 - 19 = 791$$791 \times 2 = 1582$$1582 + 216 = 1798$...</div>
                </div>
                
                <div class="item duplicate">
                    <div class="label">被去重的版本</div>
                    <div class="meta">原始索引: 2105 | 长度: 80 字符</div>
                    <div class="content">$75\times24=1800$ $9000-1800=7200$ ___ $75 \times 24 = 1800$$9000 - 1800 = 7200$...</div>
                </div>
            </div>

            <div style="margin: 10px; border-left: 3px solid #3498db; padding-left: 10px;">
                <h4>重复组 #3 <span class="similarity">相似度: 1.000</span></h4>
                
                <div class="item original">
                    <div class="label">保留的版本</div>
                    <div class="meta">原始索引: 2089 | 长度: 122 字符</div>
                    <div class="content">$810-19=791\hspace{0.33em}$ $791\times2=1582$ $1582+216=1798$ ___ $810 - 19 = 791$$791 \times 2 = 1582$$1582 + 216 = 1798$...</div>
                </div>
                
                <div class="item duplicate">
                    <div class="label">被去重的版本</div>
                    <div class="meta">原始索引: 2145 | 长度: 59 字符</div>
                    <div class="content">$960÷15=64$ $64-28=36$ ___ $960 \div 15 = 64$$64 - 28 = 36$...</div>
                </div>
            </div>

            <div style="margin: 10px; border-left: 3px solid #3498db; padding-left: 10px;">
                <h4>重复组 #4 <span class="similarity">相似度: 1.000</span></h4>
                
                <div class="item original">
                    <div class="label">保留的版本</div>
                    <div class="meta">原始索引: 2089 | 长度: 122 字符</div>
                    <div class="content">$810-19=791\hspace{0.33em}$ $791\times2=1582$ $1582+216=1798$ ___ $810 - 19 = 791$$791 \times 2 = 1582$$1582 + 216 = 1798$...</div>
                </div>
                
                <div class="item duplicate">
                    <div class="label">被去重的版本</div>
                    <div class="meta">原始索引: 2151 | 长度: 100 字符</div>
                    <div class="content">$96\times5=480$ $480+20=500$ $500÷4=125$   ___ $96 \times 5 = 480$$480 + 20 = 500$$500 \div 4 = 125$...</div>
                </div>
            </div>

            <div style="margin: 10px; border-left: 3px solid #3498db; padding-left: 10px;">
                <h4>重复组 #5 <span class="similarity">相似度: 1.000</span></h4>
                
                <div class="item original">
                    <div class="label">保留的版本</div>
                    <div class="meta">原始索引: 2093 | 长度: 299 字符</div>
                    <div class="content">将分步算式合并成一个综合算式（1） 48－33＝15 （2） 15－10＝5   15＋4＝19 21－5＝16   19×104＝1976 64÷16＝4综合算式___ 综合算式___ ( 1 ) 首先根据分步算式可以得到
48 - 33 = 15
15 + 4 = 19
19 \times 104 = 1976
因此综合算式为$( 48 - 33 + 4 ) \times 104 = 1976$
( 2 ) 根据分步算式可以得到
15 - 10 = 5
21 - 5 = 16
64 \div 16 = 4
因此综合算式为$64 \div ( 21 - ( 15 - 10 ) ) = 4$...</div>
                </div>
                
                <div class="item duplicate">
                    <div class="label">被去重的版本</div>
                    <div class="meta">原始索引: 2146 | 长度: 366 字符</div>
                    <div class="content">将分步算式合并成一个综合算式（1） 48－33＝15 （2） 15－10＝5   15＋4＝19 21－5＝16   19×104＝1976 64÷16＝4综合算式___ 综合算式___ ( 1 ) 综合算式为$( 48 - 33 + 4 ) * 104 = 1976$∵ $48 - 33 = 15$∵ $15 + 4 = 19$∴ $( 48 - 33 + 4 ) * 104 = 19 * 104 = 1976$( 2 ) 综合算式为$64 \div ( 21 - ( 48 - 33 - 10 ) ) = 4$∵ $48 - 33 = 15$∵ $15 - 10 = 5$∵ $48 - 33...</div>
                </div>
            </div>

            <div style="margin: 10px; border-left: 3px solid #3498db; padding-left: 10px;">
                <h4>重复组 #6 <span class="similarity">相似度: 1.000</span></h4>
                
                <div class="item original">
                    <div class="label">保留的版本</div>
                    <div class="meta">原始索引: 2119 | 长度: 131 字符</div>
                    <div class="content">350÷7＝50 25×6＝150 50+150＝200 ___ 首先计算每个表达式的结果
1. $350 \div 7 = 50$
2. $25 \times 6 = 150$
3. $50 + 150 = 200$
因此最终答案分别为50, 150, 200...</div>
                </div>
                
                <div class="item duplicate">
                    <div class="label">被去重的版本</div>
                    <div class="meta">原始索引: 48946 | 长度: 85 字符</div>
                    <div class="content">350÷7＝50 25×6＝150 50+150＝200 ___ $350 \div 7 = 50$$25 \times 6 = 150$$50 + 150 = 200$...</div>
                </div>
            </div>

            <div style="margin: 10px; border-left: 3px solid #3498db; padding-left: 10px;">
                <h4>重复组 #7 <span class="similarity">相似度: 1.000</span></h4>
                
                <div class="item original">
                    <div class="label">保留的版本</div>
                    <div class="meta">原始索引: 2122 | 长度: 278 字符</div>
                    <div class="content">先在下面的里填上合适的数然后列出综合算式  综合算式___    综合算式___ ( 1 ) 首先计算 182 - 62 = 120然后计算 45 + 67 = 112最后将两个结果相乘得到 120 * 112 = 13440
因此综合算式为$( 182 - 62 ) \times ( 45 + 67 ) = 13440$
( 2 ) 首先计算 23 + 27 = 50然后计算 78 \div 6 = 13最后将两个结果相乘得到 50 * 13 = 650
因此综合算式为$( 23 + 27 ) \times ( 78 \div 6 ) = 650$...</div>
                </div>
                
                <div class="item duplicate">
                    <div class="label">被去重的版本</div>
                    <div class="meta">原始索引: 2128 | 长度: 284 字符</div>
                    <div class="content">先在下面的里填上合适的数然后列出综合算式      综合算式___综合算式___ 首先我们来分析左边的算式
∵600 + 360 = 960
∴960 ÷ 24 = 40
∵40 × 30 = 1200
∴左边的综合算式为( 600 + 360 ) ÷ 24 × 30 = 1200
接下来我们来分析右边的算式
∵10 + 18 = 28
∴250 ÷ 25 = 10
∵28 ÷ 10 = 2.8（但根据题目要求应为整数故此处应为简化后的整数形式）
∴简化后的综合算式为( 10 + 18 ) ÷ ( 250 ÷ 25 ) = 50（这里假设题目要求简化为整数形式）...</div>
                </div>
            </div>

            <div style="margin: 10px; border-left: 3px solid #3498db; padding-left: 10px;">
                <h4>重复组 #8 <span class="similarity">相似度: 1.000</span></h4>
                
                <div class="item original">
                    <div class="label">保留的版本</div>
                    <div class="meta">原始索引: 2127 | 长度: 42 字符</div>
                    <div class="content">20—5＝15 240÷15＝16 ___ 20-5=15
240\div15=16...</div>
                </div>
                
                <div class="item duplicate">
                    <div class="label">被去重的版本</div>
                    <div class="meta">原始索引: 44197 | 长度: 45 字符</div>
                    <div class="content">20—5＝15 240÷15＝16 ___ $20-5=15$$240\div15=16$...</div>
                </div>
            </div>

            <div style="margin: 10px; border-left: 3px solid #3498db; padding-left: 10px;">
                <h4>重复组 #9 <span class="similarity">相似度: 1.000</span></h4>
                
                <div class="item original">
                    <div class="label">保留的版本</div>
                    <div class="meta">原始索引: 2130 | 长度: 99 字符</div>
                    <div class="content">把A+B=E,C-D=F,E×F=G 改写成一道综合算式___ \because A+B=E, C-D=F, E\times F=G\therefore ( A+B )\times( C-D )=G...</div>
                </div>
                
                <div class="item duplicate">
                    <div class="label">被去重的版本</div>
                    <div class="meta">原始索引: 2136 | 长度: 207 字符</div>
                    <div class="content">把A+B=E,C-D=F,E×F=G 改写成一道综合算式___ 根据题目给出的三个等式$A + B = E$$C - D = F$$E  imes F = G$可以将这些等式合并为一个综合算式∵ $A + B = E$又∵ $C - D = F$∴ $E  imes F = ( A + B )  imes ( C - D ) = G$因此综合算式为 $( A + B )  imes ( C - D ) = G$...</div>
                </div>
            </div>

            <div style="margin: 10px; border-left: 3px solid #3498db; padding-left: 10px;">
                <h4>重复组 #10 <span class="similarity">相似度: 1.000</span></h4>
                
                <div class="item original">
                    <div class="label">保留的版本</div>
                    <div class="meta">原始索引: 2132 | 长度: 196 字符</div>
                    <div class="content">把64-25=39117÷39=3307+3=310改写成一个综合算式是 ___ 根据题目给出的三个算式可以逐步分析并整合为一个综合算式
∵64-25=39
∴117\div39=3 可以写为 117\div( 64-25 )=3
又∵307+3=310
∴将上述结果代入得到 307+117\div( 64-25 )=310
因此综合算式为 307+117\div( 64-25 )=310...</div>
                </div>
                
                <div class="item duplicate">
                    <div class="label">被去重的版本</div>
                    <div class="meta">原始索引: 44204 | 长度: 284 字符</div>
                    <div class="content">把64-25=39117÷39=3307+3=310改写成一个综合算式是___ 根据题目给出的三个算式可以逐步分析并整合为一个综合算式
1. 第一个算式为 $64-25=39$表示 $64-25$ 的结果为 $39$
2. 第二个算式为 $117\div39=3$将第一个算式的结果 $39$ 代入得到 $117\div( 64-25 )=3$
3. 第三个算式为 $307+3=310$将第二个算式的结果 $3$ 代入得到 $307+117\div( 64-25 )=310$
因此将这三个算式整合为一个综合算式为$307+117\div( 64-25 )=310$...</div>
                </div>
            </div>

        <div class="duplicate-group">
            <div class="group-header">
                <h3>标签: 小学数学新知识树 -> 数与代数 -> 数的运算 -> 小数的四则运算 -> 小数的加法和减法 -> 利用小数的加、减法混合运算解决实际问题 -> 小数加减法应用题(涉及小数简算)</h3>
                <p>该标签下发现 1 组重复数据</p>
            </div>

            <div style="margin: 10px; border-left: 3px solid #3498db; padding-left: 10px;">
                <h4>重复组 #1 <span class="similarity">相似度: 1.000</span></h4>
                
                <div class="item original">
                    <div class="label">保留的版本</div>
                    <div class="meta">原始索引: 2268 | 长度: 203 字符</div>
                    <div class="content">一位摄影师在航拍时先将无人机上升到110.58m的高度调试调试完成先下降21.76m拍摄又上升16.42m拍摄拍摄完成后无人机从多少米的高度返航 初始高度为 $110.58$ m下降 $21.76$ m 后的高度为 $110.58 - 21.76 = 88.82$ m再上升 $16.42$ m 后的高度为 $88.82 + 16.42 = 105.24$ m因此无人机从 $105.24$ m 的高度返航...</div>
                </div>
                
                <div class="item duplicate">
                    <div class="label">被去重的版本</div>
                    <div class="meta">原始索引: 44210 | 长度: 203 字符</div>
                    <div class="content">一位摄影师在航拍时先将无人机上升到110.58m的高度调试调试完成先下降21.76m拍摄又上升16.42m拍摄拍摄完成后无人机从多少米的高度返航 初始高度为 $110.58$ m下降 $21.76$ m 后的高度为 $110.58 - 21.76 = 88.82$ m再上升 $16.42$ m 后的高度为 $88.82 + 16.42 = 105.24$ m因此无人机从 $105.24$ m 的高度返航...</div>
                </div>
            </div>

    </div>
</body>
</html>
