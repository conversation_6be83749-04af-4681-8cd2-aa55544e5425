#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
按标签分组的相似度去重脚本
先按doc_label分组，然后在每个组内对doc_token进行相似度去重
"""

import json
import re
from typing import List, Dict, Set
from collections import defaultdict
from tqdm import tqdm

class LabelGroupedDeduplicator:
    def __init__(self, input_file: str, output_file: str, threshold: float = 0.9999):
        self.input_file = input_file
        self.output_file = output_file
        self.threshold = threshold
        self.data = []
        self.label_groups = defaultdict(list)
        self.all_duplicates = []
        
    def load_data(self) -> bool:
        """加载数据"""
        try:
            print(f"📂 加载数据文件: {self.input_file}")
            with open(self.input_file, 'r', encoding='utf-8') as f:
                self.data = json.load(f)
            print(f"✅ 成功加载 {len(self.data)} 条数据")
            return True
        except Exception as e:
            print(f"❌ 加载数据失败: {str(e)}")
            return False
    
    def group_by_label(self):
        """按doc_label分组"""
        print("📊 按标签分组数据...")
        
        for i, item in enumerate(self.data):
            doc_label = item.get('doc_label', [])
            # 将标签列表转换为字符串作为分组键
            label_key = ' -> '.join(doc_label) if doc_label else '无标签'
            self.label_groups[label_key].append({
                'original_index': i,
                'item': item
            })
        
        print(f"✅ 分组完成，共 {len(self.label_groups)} 个不同的标签组")
        
        # 显示前10个最大的组
        sorted_groups = sorted(self.label_groups.items(), key=lambda x: len(x[1]), reverse=True)
        print(f"\n📈 前10个最大的标签组:")
        for i, (label, items) in enumerate(sorted_groups[:10]):
            print(f"  {i+1:2d}. {label[:80]}... ({len(items)} 条)")
    
    def extract_question_part(self, doc_token: str) -> str:
        """提取题目部分"""
        if '___' in doc_token:
            return doc_token.split('___')[0].strip()
        
        # 其他可能的分隔符
        separators = ['解：', '答：', '分析：', '解答：', '计算过程：']
        for sep in separators:
            if sep in doc_token:
                return doc_token.split(sep)[0].strip()
        
        return doc_token.strip()
    
    def normalize_text(self, text: str) -> str:
        """标准化文本"""
        # 移除多余空格
        text = re.sub(r'\s+', ' ', text.strip())
        # 统一数学符号
        text = text.replace('×', '*').replace('÷', '/')
        # 移除LaTeX符号
        text = re.sub(r'\$[^$]*\$', '', text)
        text = re.sub(r'\\[a-zA-Z]+\{[^}]*\}', '', text)
        text = re.sub(r'\\[a-zA-Z]+', '', text)
        return text.strip()
    
    def calculate_similarity(self, text1: str, text2: str) -> float:
        """计算两个文本的相似度"""
        if not text1 or not text2:
            return 0.0
        
        # 标准化文本
        text1 = self.normalize_text(text1)
        text2 = self.normalize_text(text2)
        
        if text1 == text2:
            return 1.0
        
        # 使用字符级相似度
        len1, len2 = len(text1), len(text2)
        if len1 == 0 or len2 == 0:
            return 0.0
        
        # 计算最长公共子序列长度的近似值
        common_chars = sum(1 for c1, c2 in zip(text1, text2) if c1 == c2)
        max_len = max(len1, len2)
        return common_chars / max_len
    
    def deduplicate_within_group(self, label_key: str, group_items: List[Dict]) -> List[Dict]:
        """在单个标签组内进行去重"""
        if len(group_items) <= 1:
            return group_items
        
        print(f"\n🔍 处理标签组: {label_key[:60]}... ({len(group_items)} 条)")
        
        unique_items = []
        processed_indices = set()
        group_duplicates = []
        
        for i, item_info in enumerate(group_items):
            if i in processed_indices:
                continue
            
            item = item_info['item']
            question1 = self.extract_question_part(item.get('doc_token', ''))
            similar_items = [item_info]
            
            # 查找相似的项目
            for j in range(i + 1, len(group_items)):
                if j in processed_indices:
                    continue
                
                other_item_info = group_items[j]
                other_item = other_item_info['item']
                question2 = self.extract_question_part(other_item.get('doc_token', ''))
                
                similarity = self.calculate_similarity(question1, question2)
                
                if similarity >= self.threshold:
                    similar_items.append(other_item_info)
                    processed_indices.add(j)
                    
                    # 记录重复信息
                    group_duplicates.append({
                        'label_key': label_key,
                        'question1': question1[:100],
                        'question2': question2[:100],
                        'similarity': similarity,
                        'original_index': item_info['original_index'],
                        'duplicate_index': other_item_info['original_index'],
                        'original_item': item,
                        'duplicate_item': other_item,
                        'original_length': len(item['doc_token']),
                        'duplicate_length': len(other_item['doc_token'])
                    })
                    
                    print(f"    ✅ 发现相似项 - 索引 {item_info['original_index']} 与 {other_item_info['original_index']} 相似度: {similarity:.3f}")
            
            # 选择最完整的版本（通常是最长的）
            best_item_info = max(similar_items, key=lambda x: len(x['item']['doc_token']))
            unique_items.append(best_item_info)
            processed_indices.add(i)
        
        # 保存这个组的重复信息
        if group_duplicates:
            self.all_duplicates.extend(group_duplicates)
        
        reduction = len(group_items) - len(unique_items)
        if reduction > 0:
            print(f"    📊 组内去重: {len(group_items)} → {len(unique_items)} (减少 {reduction} 条)")
        
        return unique_items
    
    def run_deduplication(self):
        """执行去重"""
        if not self.load_data():
            return
        
        # 按标签分组
        self.group_by_label()
        
        print(f"\n🚀 开始按组去重 (相似度阈值: {self.threshold})")
        print("=" * 80)
        
        all_unique_items = []
        total_original = 0
        total_duplicates_found = 0
        
        # 对每个标签组进行去重
        for label_key, group_items in tqdm(self.label_groups.items(), desc="处理标签组"):
            total_original += len(group_items)
            unique_items = self.deduplicate_within_group(label_key, group_items)
            
            # 收集去重后的数据
            for item_info in unique_items:
                all_unique_items.append(item_info['item'])
            
            duplicates_in_group = len(group_items) - len(unique_items)
            total_duplicates_found += duplicates_in_group
        
        print(f"\n" + "=" * 80)
        print(f"📊 总体去重结果:")
        print(f"   原始数据: {total_original} 条")
        print(f"   去重后: {len(all_unique_items)} 条")
        print(f"   发现重复: {total_duplicates_found} 条")
        print(f"   去重率: {(total_duplicates_found / total_original * 100):.2f}%")
        
        # 保存结果
        self.save_results(all_unique_items)
        self.save_duplicate_review()
    
    def save_results(self, unique_items: List[Dict]):
        """保存去重结果"""
        try:
            with open(self.output_file, 'w', encoding='utf-8') as f:
                json.dump(unique_items, f, ensure_ascii=False, indent=2)
            print(f"✅ 去重结果已保存到: {self.output_file}")
        except Exception as e:
            print(f"❌ 保存结果失败: {str(e)}")
    
    def save_duplicate_review(self):
        """保存重复数据审核文件"""
        if not self.all_duplicates:
            print("📋 没有发现重复数据")
            return
        
        # 保存JSON审核文件
        review_json_file = self.output_file.replace('.json', '_duplicate_review.json')
        try:
            with open(review_json_file, 'w', encoding='utf-8') as f:
                json.dump(self.all_duplicates, f, ensure_ascii=False, indent=2)
            print(f"📋 重复数据审核文件已保存到: {review_json_file}")
        except Exception as e:
            print(f"❌ 保存审核文件失败: {str(e)}")
        
        # 生成HTML审核文件
        self.generate_html_review()
    
    def generate_html_review(self):
        """生成HTML审核文件"""
        html_file = self.output_file.replace('.json', '_duplicate_review.html')
        
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>按标签分组的重复数据审核</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; }}
        .header {{ background-color: #2c3e50; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }}
        .stats {{ background-color: #17a2b8; color: white; padding: 15px; border-radius: 4px; margin-bottom: 20px; }}
        .duplicate-group {{ background-color: white; margin-bottom: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
        .group-header {{ background-color: #3498db; color: white; padding: 15px; border-radius: 8px 8px 0 0; }}
        .item {{ padding: 15px; border-bottom: 1px solid #eee; }}
        .item:last-child {{ border-bottom: none; }}
        .original {{ background-color: #e8f5e8; }}
        .duplicate {{ background-color: #fff3cd; }}
        .label {{ background-color: #6c757d; color: white; padding: 2px 6px; border-radius: 3px; font-size: 0.8em; }}
        .similarity {{ background-color: #28a745; color: white; padding: 2px 6px; border-radius: 3px; font-size: 0.8em; }}
        .content {{ background-color: #f8f9fa; padding: 10px; border-radius: 4px; margin: 10px 0; font-family: monospace; }}
        .meta {{ color: #666; font-size: 0.9em; margin: 5px 0; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>按标签分组的重复数据审核报告</h1>
            <p>相似度阈值: {self.threshold}</p>
        </div>
        
        <div class="stats">
            <strong>统计信息:</strong> 发现 {len(self.all_duplicates)} 组重复数据
        </div>
"""
        
        # 按标签分组显示重复数据
        label_duplicates = defaultdict(list)
        for dup in self.all_duplicates:
            label_duplicates[dup['label_key']].append(dup)
        
        for label_key, duplicates in list(label_duplicates.items())[:20]:  # 只显示前20个标签组
            html_content += f"""
        <div class="duplicate-group">
            <div class="group-header">
                <h3>标签: {label_key}</h3>
                <p>该标签下发现 {len(duplicates)} 组重复数据</p>
            </div>
"""
            
            for i, dup in enumerate(duplicates[:10]):  # 每个标签组最多显示10组重复
                html_content += f"""
            <div style="margin: 10px; border-left: 3px solid #3498db; padding-left: 10px;">
                <h4>重复组 #{i+1} <span class="similarity">相似度: {dup['similarity']:.3f}</span></h4>
                
                <div class="item original">
                    <div class="label">保留的版本</div>
                    <div class="meta">原始索引: {dup['original_index']} | 长度: {dup['original_length']} 字符</div>
                    <div class="content">{dup['original_item']['doc_token'][:300]}...</div>
                </div>
                
                <div class="item duplicate">
                    <div class="label">被去重的版本</div>
                    <div class="meta">原始索引: {dup['duplicate_index']} | 长度: {dup['duplicate_length']} 字符</div>
                    <div class="content">{dup['duplicate_item']['doc_token'][:300]}...</div>
                </div>
            </div>
"""
        
        html_content += """
    </div>
</body>
</html>
"""
        
        try:
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            print(f"🌐 HTML审核文件已保存到: {html_file}")
        except Exception as e:
            print(f"❌ 保存HTML审核文件失败: {str(e)}")

def main():
    """主函数"""
    input_file = "merge.json"
    output_file = "label_grouped_deduplicated.json"
    threshold = 0.999  # 相似度阈值，可以调整
    
    print("=" * 80)
    print("按标签分组的相似度去重")
    print("=" * 80)
    
    deduplicator = LabelGroupedDeduplicator(input_file, output_file, threshold)
    deduplicator.run_deduplication()
    
    print(f"\n✅ 去重完成！")
    print(f"📁 生成的文件:")
    print(f"   - {output_file}")
    print(f"   - {output_file.replace('.json', '_duplicate_review.json')}")
    print(f"   - {output_file.replace('.json', '_duplicate_review.html')}")

if __name__ == "__main__":
    main()
