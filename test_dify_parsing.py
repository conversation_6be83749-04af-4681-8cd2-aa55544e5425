#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Dify输出解析功能
"""

from process_labels_with_dify import DifyLabelProcessor

def test_parsing():
    """测试各种Dify输出格式的解析"""
    
    # 创建处理器实例（只用于测试解析功能）
    processor = DifyLabelProcessor(
        dify_api_key="test",
        workflow_id="test",
        input_json_path="test.json",
        output_json_path="test_output.json"
    )
    
    # 测试用例
    test_cases = [
        {
            "name": "标准JSON格式",
            "output": '''{"example_questions": ["1、小明有5个苹果，吃了2个，还剩几个？", "2、计算：3+4=?", "3、按从大到小排列：5, 3, 8, 1"]}''',
            "expected_count": 3
        },
        {
            "name": "JSON代码块格式",
            "output": '''```json
{
  "example_questions": [
    "1、小红有8支铅笔，用了3支，还有几支？",
    "2、计算：10-6=?",
    "3、比较大小：7○9"
  ]
}
```''',
            "expected_count": 3
        },
        {
            "name": "纯数组格式",
            "output": '''["1、有5袋盐，其中4袋每袋500g，另一袋不是500g", "2、按一定比放大后长是36厘米宽是18厘米", "3、计算分数：3/4 + 1/6"]''',
            "expected_count": 3
        },
        {
            "name": "带example_questions标签的数组",
            "output": '''"example_questions": ["1、小明买了3个苹果", "2、小红有5支笔", "3、计算：2+3=?"]''',
            "expected_count": 3
        },
        {
            "name": "多行文本格式",
            "output": '''1、小明有10个苹果，吃了3个，还剩几个？
2、计算：15+8=?
3、按从小到大排列：9, 2, 7, 4''',
            "expected_count": 3
        },
        {
            "name": "带引号和逗号的文本",
            "output": '''"1、小红有6支铅笔，用了2支，还有几支？",
"2、计算：20-5=?",
"3、比较大小：12○8"''',
            "expected_count": 3
        },
        {
            "name": "复杂JSON格式",
            "output": '''{"result": {"example_questions": ["1、有一个长方形，长是8厘米，宽是5厘米，求面积", "2、小明买了4个苹果，每个2元，一共花了多少钱？", "3、计算：25÷5=?"]}}''',
            "expected_count": 3
        }
    ]
    
    print("🔍 测试Dify输出解析功能")
    print("=" * 80)
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: {case['name']}")
        print("-" * 50)
        
        # 解析输出
        result = processor.parse_dify_output(case['output'], "测试标签路径")
        
        # 检查结果
        questions = result.get('example_questions', [])
        actual_count = len(questions)
        expected_count = case['expected_count']
        
        print(f"输出内容: {case['output'][:100]}...")
        print(f"解析出的问题数量: {actual_count}")
        print(f"期望的问题数量: {expected_count}")
        
        if actual_count == expected_count:
            print("✅ 测试通过")
            success_count += 1
        else:
            print("❌ 测试失败")
        
        # 显示解析出的问题
        print("解析出的问题:")
        for j, q in enumerate(questions[:3], 1):  # 只显示前3个
            print(f"  {j}. {q}")
        
        if len(questions) > 3:
            print(f"  ... (还有 {len(questions) - 3} 个问题)")
    
    print("\n" + "=" * 80)
    print(f"📊 测试结果统计:")
    print(f"   总测试用例: {total_count}")
    print(f"   通过: {success_count}")
    print(f"   失败: {total_count - success_count}")
    print(f"   成功率: {success_count/total_count*100:.1f}%")

def test_edge_cases():
    """测试边缘情况"""
    print("\n" + "=" * 80)
    print("🔍 测试边缘情况")
    print("=" * 80)
    
    processor = DifyLabelProcessor(
        dify_api_key="test",
        workflow_id="test", 
        input_json_path="test.json",
        output_json_path="test_output.json"
    )
    
    edge_cases = [
        {
            "name": "空输出",
            "output": "",
            "should_have_content": True
        },
        {
            "name": "纯文本无格式",
            "output": "这是一个没有格式的纯文本输出",
            "should_have_content": True
        },
        {
            "name": "错误的JSON格式",
            "output": '{"example_questions": ["1、问题1", "2、问题2"',  # 缺少结束括号
            "should_have_content": True
        },
        {
            "name": "只有一个问题",
            "output": '"1、小明有5个苹果，吃了2个，还剩几个？"',
            "should_have_content": True
        },
        {
            "name": "包含特殊字符",
            "output": '["1、计算：(3+4)×2=?", "2、分数：1/2 + 1/3 = ?", "3、百分比：25% = ?"]',
            "should_have_content": True
        }
    ]
    
    for i, case in enumerate(edge_cases, 1):
        print(f"\n边缘测试 {i}: {case['name']}")
        print("-" * 40)
        
        result = processor.parse_dify_output(case['output'], "测试标签路径")
        questions = result.get('example_questions', [])
        
        print(f"输入: {case['output']}")
        print(f"解析结果: {len(questions)} 个问题")
        
        if case['should_have_content'] and questions:
            print("✅ 正确处理")
        elif not case['should_have_content'] and not questions:
            print("✅ 正确处理")
        else:
            print("❌ 处理异常")
        
        for j, q in enumerate(questions, 1):
            print(f"  {j}. {q}")

if __name__ == "__main__":
    test_parsing()
    test_edge_cases()
